<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'username' => 'admin',
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'full_name' => 'System Administrator',
            'phone' => '0123456789',
            'role' => 'admin',
            'is_active' => true,
        ]);

        // Create manager user
        User::create([
            'username' => 'manager',
            'name' => 'Manager',
            'email' => '<EMAIL>',
            'password' => Hash::make('manager123'),
            'full_name' => 'Quản sinh',
            'phone' => '0123456788',
            'role' => 'manager',
            'is_active' => true,
        ]);

        // Create teacher user
        User::create([
            'username' => 'teacher',
            'name' => 'Teacher',
            'email' => '<EMAIL>',
            'password' => Hash::make('teacher123'),
            'full_name' => 'Giáo viên',
            'phone' => '0123456787',
            'role' => 'teacher',
            'is_active' => true,
        ]);
    }
}
