<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('donation_records', function (Blueprint $table) {
            $table->id();
            $table->enum('record_type', ['received', 'given']);
            $table->string('title', 200);
            $table->text('description')->nullable();
            $table->decimal('amount', 12, 2)->nullable();
            $table->string('donor_name', 200)->nullable();
            $table->string('donor_phone', 20)->nullable();
            $table->text('donor_address')->nullable();
            $table->string('recipient_name', 200)->nullable();
            $table->string('recipient_phone', 20)->nullable();
            $table->text('recipient_address')->nullable();
            $table->date('transaction_date');
            $table->string('category', 100)->nullable();
            $table->string('purpose', 200)->nullable();
            $table->text('notes')->nullable();
            $table->string('receipt_number', 50)->nullable();
            $table->enum('status', ['pending', 'completed', 'cancelled'])->default('completed');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->index(['record_type', 'transaction_date']);
            $table->index(['status', 'record_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('donation_records');
    }
};
