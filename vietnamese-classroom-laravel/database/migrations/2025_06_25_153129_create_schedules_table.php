<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('schedules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('class_id')->constrained('classes')->onDelete('cascade');
            $table->foreignId('teacher_id')->constrained('users')->onDelete('cascade');
            $table->integer('day_of_week'); // 1=Monday, 7=Sunday
            $table->enum('session', ['morning', 'afternoon', 'evening']);
            $table->time('start_time');
            $table->time('end_time');
            $table->string('subject', 100)->nullable();
            $table->string('room', 50)->nullable();
            $table->string('week_number', 10); // Format: 2025-W25
            $table->string('week_created', 10)->nullable(); // Week when schedule was created
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['class_id', 'week_number']);
            $table->index(['teacher_id', 'week_number']);
            $table->index(['day_of_week', 'session']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('schedules');
    }
};
