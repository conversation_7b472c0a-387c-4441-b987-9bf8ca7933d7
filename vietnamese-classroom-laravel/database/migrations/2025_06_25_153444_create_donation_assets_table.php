<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('donation_assets', function (Blueprint $table) {
            $table->id();
            $table->string('asset_name', 200);
            $table->text('description')->nullable();
            $table->string('category', 100)->nullable();
            $table->integer('quantity')->default(1);
            $table->decimal('estimated_value', 12, 2)->nullable();
            $table->enum('condition', ['new', 'good', 'fair', 'poor'])->nullable();
            $table->string('donor_name', 200)->nullable();
            $table->string('donor_phone', 20)->nullable();
            $table->text('donor_address')->nullable();
            $table->date('donation_date');
            $table->enum('status', ['received', 'distributed', 'damaged', 'lost'])->default('received');
            $table->string('distributed_to', 200)->nullable();
            $table->date('distributed_date')->nullable();
            $table->text('distribution_notes')->nullable();
            $table->string('location', 200)->nullable();
            $table->text('notes')->nullable();
            $table->foreignId('received_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('distributed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->index(['status', 'donation_date']);
            $table->index(['category', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('donation_assets');
    }
};
