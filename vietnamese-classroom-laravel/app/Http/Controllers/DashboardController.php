<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\ClassModel;
use App\Models\Student;
use App\Models\Schedule;

class DashboardController extends Controller
{
    public function index()
    {
        $stats = [
            'total_users' => User::count(),
            'total_classes' => ClassModel::count(),
            'total_students' => Student::count(),
            'total_schedules' => Schedule::count(),
        ];

        return view('dashboard', compact('stats'));
    }
}
