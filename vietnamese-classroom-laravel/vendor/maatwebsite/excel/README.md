<br />
<br />
<p align="center">
  <!-- XMAS: https://user-images.githubusercontent.com/7728097/146406114-a5f5e13a-e2ee-47a2-9bf9-ad43cdbdf200.png-->
<img width="589" src="https://user-images.githubusercontent.com/7728097/143205384-af3c73a8-0253-45f3-b5ac-28a335dddb87.png" alt="Laravel Excel logo">
</p>
<br />


<h3 align="center">Supercharged Excel exports and imports</h3>

  <p align="center">
  A simple, but elegant <a href="https://laravel.com" target="_blank">Laravel<a/> wrapper around <a href="https://phpspreadsheet.readthedocs.io/" target="_blank">PhpSpreadsheet</a>
exports and imports.
</p>

<h4 align="center">
  <a href="https://docs.laravel-excel.com/3.1/exports/">Quickstart</a>
  <span> · </span>
  <a href="https://docs.laravel-excel.com/3.1/getting-started/">Documentation</a>
  <span> · </span>
  <a href="https://course.laravel-excel.com">Video Course</a>
  <span> · </span>
  <a href="https://github.com/SpartnerNL/Laravel-Nova-Excel">Nova</a>
  <span> · </span>
  <a href="https://medium.com/maatwebsite/laravel-excel/home">Blog</a>
  <span> · </span>
  <a href="https://docs.laravel-excel.com/3.1/getting-started/contributing.html">Contributing</a>
  <span> · </span>
  <a href="https://docs.laravel-excel.com/3.1/getting-started/support.html">Support</a>
</h4>

<p align="center">
  <a href="https://github.com/SpartnerNL/Laravel-Excel/actions">
    <img src="https://github.com/Maatwebsite/Laravel-Excel/workflows/Run%20tests/badge.svg?branch=3.1" alt="Github Actions">
  </a>

  <a href="https://styleci.io/repos/14259390">
    <img src="https://styleci.io/repos/14259390/shield?branch=3.1" alt="StyleCI">
  </a>

   <a href="https://packagist.org/packages/maatwebsite/excel">
      <img src="https://poser.pugx.org/maatwebsite/excel/v/stable.png" alt="Latest Stable Version">
  </a>

  <a href="https://packagist.org/packages/maatwebsite/excel">
      <img src="https://poser.pugx.org/maatwebsite/excel/downloads.png" alt="Total Downloads">
  </a>

  <a href="https://packagist.org/packages/maatwebsite/excel">
    <img src="https://poser.pugx.org/maatwebsite/excel/license.png" alt="License">
  </a>
</p>

## ✨ Features

- **Easily export collections to Excel.** Supercharge your Laravel collections and export them directly to an Excel or CSV document. Exporting has never been so easy.

- **Supercharged exports.** Export queries with automatic chunking for better performance. You provide us the query, we handle the performance. Exporting even larger datasets? No worries, Laravel Excel has your back. You can queue your exports so all of this happens in the background.

- **Supercharged imports.** Import workbooks and worksheets to Eloquent models with chunk reading and batch inserts! Have large files? You can queue every chunk of a file! Your entire import will happen in the background.

- **Export Blade views.** Want to have a custom layout in your spreadsheet? Use a HTML table in a Blade view and export that to Excel.

![banner](https://user-images.githubusercontent.com/7728097/57463977-2263fc80-727c-11e9-833d-669d816fb7fb.jpg)
<br>

## 🎓 Learning Laravel Excel

You can find the full documentation of Laravel Excel [on the website](https://docs.laravel-excel.com).

We welcome suggestions for improving our docs. The documentation repository can be found at [https://github.com/SpartnerNL/laravel-excel-docs](https://github.com/SpartnerNL/laravel-excel-docs).

Some articles and tutorials can be found on our blog: https://medium.com/maatwebsite/laravel-excel/home

## :mailbox_with_mail: License & Postcardware

![1_5nblgs68uarg0wxxejozdq](https://user-images.githubusercontent.com/7728097/53638144-9e5f1a00-3c25-11e9-9f4a-fc71c9d94562.jpg)

Laravel Excel is created with love and care by Spartner (formerly known as Maatwebsite) to give back to the Laravel community. It is completely free (MIT license) to use, however the package is licensed as Postcardware. This means that if it makes it to your production environment, we would very much appreciate receiving a postcard from your hometown.

**Spartner**
Markt 2
6231 LS Meerssen
The Netherlands.

More about the license can be found at: [https://docs.laravel-excel.com/3.1/getting-started/license.html](https://docs.laravel-excel.com/3.1/getting-started/license.html)

## Created by Spartner (formerly Maatwebsite)

We are a strategic development partner, creating web-based custom built software from Laravel. In need of a digital solution for your challenge? Give us a call.

https://spartner.software
<EMAIL>
+31 (0) 10 - 7449312

## :wrench: Supported Versions

Versions will be supported for a limited amount of time.

| Version | Laravel Version | Php Version | Support |
|---- |----|----|----|
| 2.1 | <=5.6 | <=7.0 | Unsupported since 15-5-2018 |
| 3.0 | ^5.5 |  ^7.0 | Unsupported since 31-12-2018 |
| 3.1 | >=5.8 \| <=11.x |  ^7.2 \| ^8.0 | New features |
