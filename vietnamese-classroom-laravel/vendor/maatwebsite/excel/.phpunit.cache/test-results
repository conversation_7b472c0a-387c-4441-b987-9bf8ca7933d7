{"version": 1, "defects": {"Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_queue_an_import_with_batch_cache": 5, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_queue_an_export_with_batch_cache": 8, "Maatwebsite\\Excel\\Tests\\QueuedQueryExportTest::can_queue_an_export_with_batch_cache": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_query": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_relation_query_queued": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_query_with_eager_loads": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_query_with_eager_loads_and_queued": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_query_builder_without_using_eloquent": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_query_builder_without_using_eloquent_and_queued": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_query_builder_with_nested_arrays": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_query_builder_with_nested_arrays_queued": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_query_with_batch_caching": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_query_with_prepare_rows": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_scout": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromViewTest::can_export_from_view": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromViewTest::can_export_multiple_sheets_from_view": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::can_import_a_simple_xlsx_file": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::can_import_a_simple_xlsx_file_from_uploaded_file": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::can_import_a_simple_csv_file_with_html_tags_inside": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::can_import_a_simple_xlsx_file_with_ignore_empty_set_to_true": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::can_import_a_simple_xlsx_file_with_ignore_empty_set_to_false": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\OnEachRowTest::can_import_each_row_individually": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\OnEachRowTest::it_respects_the_end_column": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\RegistersEventListenersTest::events_get_called_when_importing": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersChunkOffsetTest::can_access_chunk_offset_on_import_to_array_in_chunks": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersRowNumberTest::can_access_row_number_on_import_to_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersRowNumberTest::can_access_row_number_on_import_to_array_in_chunks": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersRowNumberTest::can_access_row_number_on_import_to_array_in_chunks_with_batch_inserts": 8, "ShouldQueueWithoutChainTest::can_import_to_model_in_chunks": 8, "ShouldQueueWithoutChainTest::can_import_to_model_without_job_chaining": 8, "ShouldQueueWithoutChainTest::a_queue_name_can_be_specified_when_importing": 8, "ShouldQueueWithoutChainTest::the_cleanup_only_runs_when_all_jobs_are_done": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::skips_empty_rows_when_importing_to_collection": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::skips_empty_rows_when_importing_on_each_row": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::skips_empty_rows_when_importing_to_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::custom_skips_rows_when_importing_to_collection": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::custom_skips_rows_when_importing_to_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::custom_skips_rows_when_using_oneachrow": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnErrorTest::can_skip_on_error": 7, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnErrorTest::can_skip_errors_and_collect_all_errors_at_the_end": 7, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::can_skip_on_error": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::skips_only_failed_rows_in_batch": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::can_skip_failures_and_collect_all_failures_at_the_end": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::can_validate_using_oneachrow_and_skipsonfailure": 7, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::can_validate_using_tocollection_and_skipsonfailure": 7, "Maatwebsite\\Excel\\Tests\\Concerns\\ToArrayTest::can_import_to_array": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ToArrayTest::can_import_multiple_sheets_to_array": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ToCollectionTest::can_import_to_collection": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ToCollectionTest::can_import_multiple_sheets_to_collection": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::can_import_each_row_to_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::has_timestamps_when_imported_single_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::can_import_multiple_models_in_single_to_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::can_import_multiple_different_types_of_models_in_single_to_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBatchInsertsTest::can_import_to_model_in_batches": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBatchInsertsTest::can_import_to_model_in_batches_bigger_file": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBatchInsertsTest::can_import_multiple_different_types_of_models_in_single_to_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBatchInsertsTest::has_timestamps_when_imported_in_batches": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::by_default_does_not_calculate_formulas": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::can_import_to_array_with_calculated_formulas": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::can_import_to_model_with_calculated_formulas": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::can_import_to_array_with_calculated_formulas_and_multi_sheet_references": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::can_import_to_array_with_calculated_formulas_and_skips_empty": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::can_import_to_model_with_calculated_formulas_and_skips_empty": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_to_model_in_chunks_un": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_to_model_in_chunks_and_insert_in_batches": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_to_model_in_chunks_and_insert_in_batches_with_heading_row": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_csv_in_chunks_and_insert_in_batches": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_to_model_in_chunks_and_insert_in_batches_with_multiple_sheets": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_to_array_in_chunks": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_to_model_in_chunks_and_insert_in_batches_with_multiple_sheets_objects_by_index": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_to_model_in_chunks_and_insert_in_batches_with_multiple_sheets_objects_by_name": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_catch_job_failed_in_chunks": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_to_array_and_format_in_chunks": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_to_array_in_chunks_without_formatting": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithColumnLimitTest::can_import_to_array_with_column_limit": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithColumnLimitTest::can_import_to_array_with_column_limit_and_skips_empty_rows": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithConditionalSheetsTest::can_select_which_sheets_will_be_imported": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomCsvSettingsTest::can_read_csv_import_with_custom_settings": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomCsvSettingsTest::cannot_read_with_wrong_delimiter": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomQuerySizeTest::can_export_with_custom_count": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomValueBinderTest::can_set_a_value_binder_on_import": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::import_events_get_called": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::import_chunked_events_get_called": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::by_default_import_to_array": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::can_import_to_array_with_format_data": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::can_import_to_array_with_format_data_and_skips_empty_rows": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::by_default_import_to_collection": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::can_import_to_collection_with_format_data": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::by_default_import_to_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::can_import_to_model_with_format_data": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithGroupedHeadingRowTest::can_import_to_array_with_grouped_headers": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithGroupedHeadingRowTest::can_import_oneachrow_with_grouped_headers": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithGroupedHeadingRowTest::can_import_to_collection_with_grouped_headers": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithGroupedHeadingRowTest::can_import_each_row_to_model_with_grouped_headers": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::can_import_each_row_to_model_with_heading_row": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::can_import_each_row_to_model_with_different_heading_row": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::can_import_to_array_with_heading_row": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::can_import_empty_rows_with_header": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::can_import_empty_models_with_header": 7, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::can_cast_empty_headers_to_indexed_int": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithLimitTest::can_import_a_limited_section_of_rows_to_model_with_different_start_row": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithLimitTest::can_import_to_array_with_limit": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithLimitTest::can_set_limit_bigger_than_row_size": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappedCellsTest::can_import_with_references_to_cells": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappedCellsTest::can_import_with_nested_references_to_cells": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappedCellsTest::can_import_with_references_to_cells_to_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::can_export_with_multiple_sheets_using_collections": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::can_export_multiple_sheets_from_view": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::unknown_sheet_index_will_throw_sheet_not_found_exception": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::unknown_sheet_name_will_throw_sheet_not_found_exception": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::unknown_sheet_name_can_be_ignored": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::unknown_sheet_indices_can_be_ignored_per_name": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::unknown_sheet_indices_can_be_ignored": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::unknown_sheet_indices_can_be_ignored_per_sheet": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::can_import_multiple_sheets": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::can_import_multiple_sheets_by_sheet_name": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::can_import_multiple_sheets_by_sheet_index_and_name": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::can_import_multiple_sheets_by_sheet_name_and_index": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStartRowTest::can_import_each_row_to_model_with_different_start_row": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStartRowTest::can_import_to_array_with_start_row": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithUpsertsTest::can_upsert_models_in_batches": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithUpsertsTest::can_upsert_models_in_rows": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithUpsertsTest::can_upsert_models_in_batches_with_defined_upsert_columns": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithUpsertsTest::can_upsert_models_in_rows_with_defined_upsert_columns": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_rows": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_rows_with_closure_validation_rules": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_rows_with_custom_validation_rule_objects": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_rows_with_conditionality": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_rows_with_unless_conditionality": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_rows_with_combined_rules_with_colons": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_with_custom_attributes": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_with_custom_message": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_rows_with_headings": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_rows_with_grouped_headings": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_rows_in_batches": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_using_oneachrow": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_using_collection": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_using_array": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_configure_validator": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_prepare_using_toarray": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_prepare_using_tocollection": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_prepare_using_tomodel": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_prepare_using_oneachrow": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_prepare_using_skipsemptyrows": 8, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_import_a_simple_xlsx_file": 8, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_import_a_tsv_file": 8, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_chain_imports": 8, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_import_a_simple_xlsx_file_from_uploaded_file": 8, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_import_a_simple_xlsx_file_from_real_path": 8, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_import_without_extension_with_explicit_reader_type": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreCollectionTest::can_store_a_model_collection_with_headings_as_excel": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::cannot_queue_import_that_does_not_implement_should_queue": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_queue_an_import": 7, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_queue_an_import_with_batch_cache_and_file_store": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_queue_import_with_remote_temp_disk": 7, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_keep_extension_for_temp_file_on_remote_disk": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_queue_import_with_remote_temp_disk_and_prefix": 7, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_automatically_delete_temp_file_on_failure_when_using_remote_disk": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::cannot_automatically_delete_temp_file_on_failure_when_using_local_disk": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_force_remote_download_and_deletion_for_each_chunk_on_queue": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_define_middleware_method_on_queued_import": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_define_retry_until_method_on_queued_import": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_define_max_exceptions_property_on_queued_import": 8, "Maatwebsite\\Excel\\Tests\\QueuedQueryExportTest::can_queue_an_export": 8, "Maatwebsite\\Excel\\Tests\\QueuedQueryExportTest::can_queue_an_export_with_batch_cache_and_file_store": 8, "Maatwebsite\\Excel\\Tests\\QueuedQueryExportTest::can_queue_an_export_with_mapping": 8, "Maatwebsite\\Excel\\Tests\\QueuedQueryExportTest::can_queue_scout_export": 8, "Maatwebsite\\Excel\\Tests\\QueuedViewExportTest::can_queue_an_export": 8, "Maatwebsite\\Excel\\Tests\\QueuedViewExportTest::can_export_multiple_sheets_from_view": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromCollectionTest::can_export_from_lazy_collection": 8, "Maatwebsite\\Excel\\Tests\\CellTest::can_get_cell_value": 7, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::export_chunked_events_get_called": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromArrayTest::can_export_from_array": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromCollectionTest::can_export_from_collection": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromCollectionTest::can_export_with_multiple_sheets_from_collection": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromCollectionTest::can_export_from_lazy_collection_with_queue": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithColumnFormattingTest::can_export_with_column_formatting": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithColumnWidthsTest::can_set_column_width": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomCsvSettingsTest::can_store_csv_export_with_custom_settings": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomCsvSettingsTest::can_store_csv_export_with_custom_encoding": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomStartCellTest::can_store_collection_with_custom_start_cell": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomValueBinderTest::can_set_a_value_binder_on_export": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithDefaultStylesTest::can_configure_default_styles": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingsTest::can_export_from_collection_with_heading_row": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingsTest::can_export_from_collection_with_multiple_heading_rows": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingsTest::can_export_from_collection_with_heading_row_with_custom_start_cell": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappingTest::can_export_with_heading": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappingTest::can_return_multiple_rows_in_map": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappingTest::json_array_columns_shouldnt_be_detected_as_multiple_rows": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStrictNullComparisonTest::exported_zero_values_are_not_null_when_exporting_with_strict_null_comparison": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStrictNullComparisonTest::exported_zero_values_are_null_when_not_exporting_with_strict_null_comparison": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStrictNullComparisonTest::exports_trailing_empty_cells": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStrictNullComparisonTest::exports_trailing_empty_cells_by_setting_config_strict_null_comparison": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStylesTest::can_configure_styles": 8, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_store_csv_export_with_custom_settings": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadCollectionTest::can_download_a_collection_as_excel": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadCollectionTest::can_download_a_collection_with_headers_as_excel": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadCollectionTest::can_download_collection_with_headers_with_hidden_eloquent_attributes": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadCollectionTest::can_download_collection_with_headers_when_making_attributes_visible": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadCollectionTest::can_set_custom_response_headers": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadQueryMacroTest::can_download_a_query_as_excel": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadQueryMacroTest::can_download_a_collection_with_headers_as_excel": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\ImportAsMacroTest::can_import_directly_into_a_model_with_mapping": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\ImportMacroTest::can_import_directly_into_a_model": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreCollectionTest::can_store_a_collection_as_excel": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreCollectionTest::can_store_a_collection_as_excel_on_non_default_disk": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreCollectionTest::can_store_a_collection_with_headings_as_excel": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreQueryMacroTest::can_download_a_query_as_excel": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreQueryMacroTest::can_download_a_query_as_excel_on_different_disk": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreQueryMacroTest::can_store_a_query_with_headers_as_excel": 8, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_queue_an_export": 8, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_queue_an_export_and_store_on_different_disk": 8, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_queue_export_with_remote_temp_disk": 8, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_queue_export_with_remote_temp_disk_and_prefix": 8, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_implicitly_queue_an_export": 8, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_queue_export_with_mapping_on_eloquent_models": 8, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_set_locale_on_queue_export_job": 8, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_queue_export_not_flushing_the_cache": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::can_import_models_with_belongs_to_relations": 7, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::can_import_models_with_belongs_to_many_relations": 7, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::it_writes_to_cache_with_a_dateinterval_ttl": 7, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::can_import_only_heading_row": 5, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::can_import_only_heading_row_with_custom_heading_row_formatter": 5, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::can_import_only_heading_row_with_custom_heading_row_formatter_with_key": 5, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::can_import_only_heading_row_with_custom_row_number": 5, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::can_import_only_heading_row_for_multiple_sheets": 5, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::can_import_only_heading_row_for_multiple_sheets_with_key": 5, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::can_import_only_heading_row_for_multiple_sheets_with_custom_row_number": 5, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::can_import_heading_row_with_custom_formatter_defined_in_config": 5, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::test_cannot_import_a_non_existing_xlsx_file": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_query": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_relation_query_queued": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_query_with_eager_loads": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_query_with_eager_loads_and_queued": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_query_builder_without_using_eloquent": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_query_builder_without_using_eloquent_and_queued": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_query_builder_with_nested_arrays": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_query_builder_with_nested_arrays_queued": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_query_with_batch_caching": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_query_with_prepare_rows": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_scout": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromViewTest::test_can_export_from_view": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\FromViewTest::test_can_export_multiple_sheets_from_view": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::test_can_import_a_simple_xlsx_file": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::test_can_import_a_simple_xlsx_file_from_uploaded_file": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::test_can_import_a_simple_csv_file_with_html_tags_inside": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::test_can_import_a_simple_xlsx_file_with_ignore_empty_set_to_true": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::test_can_import_a_simple_xlsx_file_with_ignore_empty_set_to_false": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\OnEachRowTest::test_can_import_each_row_individually": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\OnEachRowTest::test_it_respects_the_end_column": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\RegistersEventListenersTest::test_events_get_called_when_importing": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersChunkOffsetTest::test_can_access_chunk_offset_on_import_to_array_in_chunks": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersRowNumberTest::test_can_access_row_number_on_import_to_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersRowNumberTest::test_can_access_row_number_on_import_to_array_in_chunks": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersRowNumberTest::test_can_access_row_number_on_import_to_array_in_chunks_with_batch_inserts": 8, "ShouldQueueWithoutChainTest::test_can_import_to_model_in_chunks": 8, "ShouldQueueWithoutChainTest::test_can_import_to_model_without_job_chaining": 8, "ShouldQueueWithoutChainTest::test_a_queue_name_can_be_specified_when_importing": 8, "ShouldQueueWithoutChainTest::test_the_cleanup_only_runs_when_all_jobs_are_done": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::test_skips_empty_rows_when_importing_to_collection": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::test_skips_empty_rows_when_importing_on_each_row": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::test_skips_empty_rows_when_importing_to_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::test_custom_skips_rows_when_importing_to_collection": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::test_custom_skips_rows_when_importing_to_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::test_custom_skips_rows_when_using_oneachrow": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnErrorTest::test_can_skip_on_error": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnErrorTest::test_can_skip_errors_and_collect_all_errors_at_the_end": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::test_can_skip_on_error": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::test_skips_only_failed_rows_in_batch": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::test_can_skip_failures_and_collect_all_failures_at_the_end": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::test_can_validate_using_oneachrow_and_skipsonfailure": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::test_can_validate_using_tocollection_and_skipsonfailure": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ToArrayTest::test_can_import_to_array": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ToArrayTest::test_can_import_multiple_sheets_to_array": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ToCollectionTest::test_can_import_to_collection": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ToCollectionTest::test_can_import_multiple_sheets_to_collection": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::test_can_import_each_row_to_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::test_has_timestamps_when_imported_single_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::test_can_import_multiple_models_in_single_to_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::test_can_import_multiple_different_types_of_models_in_single_to_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::test_can_import_models_with_belongs_to_relations": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::test_can_import_models_with_belongs_to_many_relations": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBatchInsertsTest::test_can_import_to_model_in_batches": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBatchInsertsTest::test_can_import_to_model_in_batches_bigger_file": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBatchInsertsTest::test_can_import_multiple_different_types_of_models_in_single_to_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBatchInsertsTest::test_has_timestamps_when_imported_in_batches": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::test_by_default_does_not_calculate_formulas": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::test_can_import_to_array_with_calculated_formulas": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::test_can_import_to_model_with_calculated_formulas": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::test_can_import_to_array_with_calculated_formulas_and_multi_sheet_references": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::test_can_import_to_array_with_calculated_formulas_and_skips_empty": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::test_can_import_to_model_with_calculated_formulas_and_skips_empty": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_to_model_in_chunks_un": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_to_model_in_chunks_and_insert_in_batches": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_to_model_in_chunks_and_insert_in_batches_with_heading_row": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_csv_in_chunks_and_insert_in_batches": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_to_model_in_chunks_and_insert_in_batches_with_multiple_sheets": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_to_array_in_chunks": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_to_model_in_chunks_and_insert_in_batches_with_multiple_sheets_objects_by_index": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_to_model_in_chunks_and_insert_in_batches_with_multiple_sheets_objects_by_name": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_catch_job_failed_in_chunks": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_to_array_and_format_in_chunks": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_to_array_in_chunks_without_formatting": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithColumnLimitTest::test_can_import_to_array_with_column_limit": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithColumnLimitTest::test_can_import_to_array_with_column_limit_and_skips_empty_rows": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithConditionalSheetsTest::test_can_select_which_sheets_will_be_imported": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomCsvSettingsTest::test_can_read_csv_import_with_custom_settings": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomCsvSettingsTest::test_cannot_read_with_wrong_delimiter": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomQuerySizeTest::test_can_export_with_custom_count": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomValueBinderTest::test_can_set_a_value_binder_on_import": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::test_import_events_get_called": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::test_import_chunked_events_get_called": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::test_export_chunked_events_get_called": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::test_by_default_import_to_array": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::test_can_import_to_array_with_format_data": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::test_can_import_to_array_with_format_data_and_skips_empty_rows": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::test_by_default_import_to_collection": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::test_can_import_to_collection_with_format_data": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::test_by_default_import_to_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::test_can_import_to_model_with_format_data": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithGroupedHeadingRowTest::test_can_import_to_array_with_grouped_headers": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithGroupedHeadingRowTest::test_can_import_oneachrow_with_grouped_headers": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithGroupedHeadingRowTest::test_can_import_to_collection_with_grouped_headers": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithGroupedHeadingRowTest::test_can_import_each_row_to_model_with_grouped_headers": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::test_can_import_each_row_to_model_with_heading_row": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::test_can_import_each_row_to_model_with_different_heading_row": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::test_can_import_to_array_with_heading_row": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::test_can_import_empty_rows_with_header": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::test_can_import_empty_models_with_header": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::test_can_cast_empty_headers_to_indexed_int": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithLimitTest::test_can_import_a_limited_section_of_rows_to_model_with_different_start_row": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithLimitTest::test_can_import_to_array_with_limit": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithLimitTest::test_can_set_limit_bigger_than_row_size": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappedCellsTest::test_can_import_with_references_to_cells": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappedCellsTest::test_can_import_with_nested_references_to_cells": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappedCellsTest::test_can_import_with_references_to_cells_to_model": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_can_export_with_multiple_sheets_using_collections": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_can_export_multiple_sheets_from_view": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_unknown_sheet_index_will_throw_sheet_not_found_exception": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_unknown_sheet_name_will_throw_sheet_not_found_exception": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_unknown_sheet_name_can_be_ignored": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_unknown_sheet_indices_can_be_ignored_per_name": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_unknown_sheet_indices_can_be_ignored": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_unknown_sheet_indices_can_be_ignored_per_sheet": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_can_import_multiple_sheets": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_can_import_multiple_sheets_by_sheet_name": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_can_import_multiple_sheets_by_sheet_index_and_name": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_can_import_multiple_sheets_by_sheet_name_and_index": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStartRowTest::test_can_import_each_row_to_model_with_different_start_row": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStartRowTest::test_can_import_to_array_with_start_row": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithUpsertsTest::test_can_upsert_models_in_batches": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithUpsertsTest::test_can_upsert_models_in_rows": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithUpsertsTest::test_can_upsert_models_in_batches_with_defined_upsert_columns": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithUpsertsTest::test_can_upsert_models_in_rows_with_defined_upsert_columns": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_rows": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_rows_with_closure_validation_rules": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_rows_with_custom_validation_rule_objects": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_rows_with_conditionality": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_rows_with_unless_conditionality": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_rows_with_combined_rules_with_colons": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_with_custom_attributes": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_with_custom_attributes_pointing_to_another_attribute": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_with_custom_message": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_rows_with_headings": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_rows_with_grouped_headings": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_rows_in_batches": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_using_oneachrow": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_using_collection": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_using_array": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_configure_validator": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_prepare_using_toarray": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_prepare_using_tocollection": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_prepare_using_tomodel": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_prepare_using_oneachrow": 8, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_prepare_using_skipsemptyrows": 8, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_import_a_simple_xlsx_file": 8, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_import_a_tsv_file": 8, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_chain_imports": 8, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_import_a_simple_xlsx_file_from_uploaded_file": 8, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_import_a_simple_xlsx_file_from_real_path": 8, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_import_without_extension_with_explicit_reader_type": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadQueryMacroTest::test_can_download_a_query_as_excel": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadQueryMacroTest::test_can_download_a_collection_with_headers_as_excel": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\ImportAsMacroTest::test_can_import_directly_into_a_model_with_mapping": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\ImportMacroTest::test_can_import_directly_into_a_model": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreCollectionTest::test_can_store_a_model_collection_with_headings_as_excel": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreQueryMacroTest::test_can_download_a_query_as_excel": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreQueryMacroTest::test_can_download_a_query_as_excel_on_different_disk": 8, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreQueryMacroTest::test_can_store_a_query_with_headers_as_excel": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_cannot_queue_import_that_does_not_implement_should_queue": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_queue_an_import": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_queue_an_import_with_batch_cache_and_file_store": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_queue_import_with_remote_temp_disk": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_keep_extension_for_temp_file_on_remote_disk": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_queue_import_with_remote_temp_disk_and_prefix": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_automatically_delete_temp_file_on_failure_when_using_remote_disk": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_cannot_automatically_delete_temp_file_on_failure_when_using_local_disk": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_force_remote_download_and_deletion_for_each_chunk_on_queue": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_define_middleware_method_on_queued_import": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_define_retry_until_method_on_queued_import": 8, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_define_max_exceptions_property_on_queued_import": 8, "Maatwebsite\\Excel\\Tests\\QueuedQueryExportTest::test_can_queue_an_export": 8, "Maatwebsite\\Excel\\Tests\\QueuedQueryExportTest::test_can_queue_an_export_with_batch_cache_and_file_store": 8, "Maatwebsite\\Excel\\Tests\\QueuedQueryExportTest::test_can_queue_an_export_with_mapping": 8, "Maatwebsite\\Excel\\Tests\\QueuedQueryExportTest::test_can_queue_scout_export": 8, "Maatwebsite\\Excel\\Tests\\QueuedViewExportTest::test_can_queue_an_export": 8, "Maatwebsite\\Excel\\Tests\\QueuedViewExportTest::test_can_export_multiple_sheets_from_view": 8, "Maatwebsite\\Excel\\Tests\\Validators\\RowValidatorTest::test_format_rule_with_string_input_matching_pattern": 7, "Maatwebsite\\Excel\\Tests\\Validators\\RowValidatorTest::test_format_rule_with_required_without_all": 7, "Maatwebsite\\Excel\\Tests\\Validators\\RowValidatorTest::test_format_rule_with_required_without": 7}, "times": {"Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_queue_an_import_with_batch_cache": 2.346, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_queue_an_export": 0.241, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_queue_an_export_with_batch_cache": 0.067, "Maatwebsite\\Excel\\Tests\\QueuedQueryExportTest::can_queue_an_export_with_batch_cache": 0.374, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::will_get_multiple_from_memory_if_cells_hold_in_memory": 0.02, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::will_get_multiple_from_cache_if_cells_are_persisted": 0.002, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::will_get_multiple_from_cache_and_persisted": 0.001, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::it_persists_to_cache_when_memory_limit_reached_on_setting_a_value": 0, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::it_persists_to_cache_when_memory_limit_reached_on_setting_multiple_values": 0, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::it_writes_to_cache_with_default_ttl#null (forever)": 0.001, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::it_writes_to_cache_with_default_ttl#int value": 0, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::it_writes_to_cache_with_default_ttl#callable": 0, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::it_writes_to_cache_with_a_dateinterval_ttl": 0, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::it_can_override_default_ttl": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::needs_to_have_a_file_name_when_downloading": 0.002, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::needs_to_have_a_file_name_when_storing": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::needs_to_have_a_file_name_when_queuing": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::responsable_needs_to_have_file_name_configured_inside_the_export": 0.001, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::is_responsable": 0.037, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::can_have_customized_header": 0.002, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::can_set_custom_headers_in_export_class": 0.001, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::can_get_raw_export_contents": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::can_have_customized_disk_options_when_storing": 0.007, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::can_have_customized_disk_options_when_queueing": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::can_set_disk_options_in_export_class_when_storing": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::can_set_disk_options_in_export_class_when_queuing": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::can_override_export_class_disk_options_when_calling_store": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::can_override_export_class_disk_options_when_calling_queue": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::can_have_empty_disk_options_when_storing": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::can_have_empty_disk_options_when_queueing": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\FromArrayTest::can_export_from_array": 0.011, "Maatwebsite\\Excel\\Tests\\Concerns\\FromCollectionTest::can_export_from_collection": 0.036, "Maatwebsite\\Excel\\Tests\\Concerns\\FromCollectionTest::can_export_with_multiple_sheets_from_collection": 0.139, "Maatwebsite\\Excel\\Tests\\Concerns\\FromCollectionTest::can_export_from_lazy_collection": 0.007, "Maatwebsite\\Excel\\Tests\\Concerns\\FromGeneratorTest::can_export_from_generator": 0.007, "Maatwebsite\\Excel\\Tests\\Concerns\\FromIteratorTest::can_export_from_iterator": 0.007, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::can_import_a_simple_xlsx_file": 0.006, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::can_import_a_simple_xlsx_file_from_uploaded_file": 0.008, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::can_import_a_simple_csv_file_with_html_tags_inside": 0.006, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::can_import_a_simple_xlsx_file_with_ignore_empty_set_to_true": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::can_import_a_simple_xlsx_file_with_ignore_empty_set_to_false": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\OnEachRowTest::can_import_each_row_individually": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\OnEachRowTest::it_respects_the_end_column": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\RegistersEventListenersTest::events_get_called_when_exporting": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\RegistersEventListenersTest::events_get_called_when_importing": 0.01, "Maatwebsite\\Excel\\Tests\\Concerns\\RegistersEventListenersTest::can_have_invokable_class_as_listener": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersChunkOffsetTest::can_set_and_get_chunk_offset": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersChunkOffsetTest::can_access_chunk_offset_on_import_to_array_in_chunks": 0.249, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersRowNumberTest::can_set_and_get_row_number": 0.001, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersRowNumberTest::can_access_row_number_on_import_to_model": 0.185, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersRowNumberTest::can_access_row_number_on_import_to_array_in_chunks": 3.444, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersRowNumberTest::can_access_row_number_on_import_to_array_in_chunks_with_batch_inserts": 3.44, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::skips_empty_rows_when_importing_to_collection": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::skips_empty_rows_when_importing_on_each_row": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::skips_empty_rows_when_importing_to_model": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::custom_skips_rows_when_importing_to_collection": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::custom_skips_rows_when_importing_to_model": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::custom_skips_rows_when_using_oneachrow": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\ToArrayTest::can_import_to_array": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\ToArrayTest::can_import_multiple_sheets_to_array": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\ToCollectionTest::can_import_to_collection": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\ToCollectionTest::can_import_multiple_sheets_to_collection": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBackgroundColorTest::can_configure_background_color_from_rgb_string": 0.007, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBackgroundColorTest::can_configure_background_color_as_array": 0.006, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBackgroundColorTest::can_configure_background_color_with_color_instance": 0.006, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::by_default_does_not_calculate_formulas": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::can_import_to_array_with_calculated_formulas": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::can_import_to_model_with_calculated_formulas": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::can_import_to_array_with_calculated_formulas_and_multi_sheet_references": 0.006, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::can_import_to_array_with_calculated_formulas_and_skips_empty": 0.018, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::can_import_to_model_with_calculated_formulas_and_skips_empty": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithColumnFormattingTest::can_export_with_column_formatting": 0.008, "Maatwebsite\\Excel\\Tests\\Concerns\\WithColumnWidthsTest::can_set_column_width": 0.007, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomCsvSettingsTest::can_store_csv_export_with_custom_settings": 0.002, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomCsvSettingsTest::can_store_csv_export_with_custom_encoding": 0.001, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomCsvSettingsTest::can_read_csv_with_auto_detecting_delimiter_semicolon": 0.002, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomCsvSettingsTest::can_read_csv_with_auto_detecting_delimiter_comma": 0.002, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomCsvSettingsTest::can_read_csv_import_with_custom_settings": 0.003, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomCsvSettingsTest::cannot_read_with_wrong_delimiter": 0.002, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomStartCellTest::can_store_collection_with_custom_start_cell": 0.003, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomValueBinderTest::can_set_a_value_binder_on_export": 0.011, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomValueBinderTest::can_set_a_value_binder_on_import": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\WithDefaultStylesTest::can_configure_default_styles": 0.007, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::export_events_get_called": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::import_events_get_called": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::import_chunked_events_get_called": 0.499, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::can_have_invokable_class_as_listener": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::can_have_global_event_listeners": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::can_have_custom_concern_handlers": 0.012, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::can_have_custom_sheet_concern_handlers": 0.011, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::by_default_import_to_array": 0.008, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::can_import_to_array_with_format_data": 0.007, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::can_import_to_array_with_format_data_and_skips_empty_rows": 0.006, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::by_default_import_to_collection": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::can_import_to_collection_with_format_data": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::by_default_import_to_model": 0.006, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::can_import_to_model_with_format_data": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingsTest::can_export_from_collection_with_heading_row": 0.007, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingsTest::can_export_from_collection_with_multiple_heading_rows": 0.021, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingsTest::can_export_from_collection_with_heading_row_with_custom_start_cell": 0.007, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappingTest::can_export_with_heading": 0.007, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappingTest::can_return_multiple_rows_in_map": 0.007, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappingTest::json_array_columns_shouldnt_be_detected_as_multiple_rows": 0.007, "Maatwebsite\\Excel\\Tests\\Concerns\\WithPropertiesTest::can_set_custom_document_properties": 0.006, "Maatwebsite\\Excel\\Tests\\Concerns\\WithPropertiesTest::it_merges_with_default_properties": 0.006, "Maatwebsite\\Excel\\Tests\\Concerns\\WithPropertiesTest::it_ignores_empty_properties": 0.006, "Maatwebsite\\Excel\\Tests\\Concerns\\WithReadFilterTest::can_register_custom_read_filter": 0.003, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStrictNullComparisonTest::exported_zero_values_are_not_null_when_exporting_with_strict_null_comparison": 0.007, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStrictNullComparisonTest::exported_zero_values_are_null_when_not_exporting_with_strict_null_comparison": 0.006, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStrictNullComparisonTest::exports_trailing_empty_cells": 0.003, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStrictNullComparisonTest::exports_trailing_empty_cells_by_setting_config_strict_null_comparison": 0.003, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStylesTest::can_configure_styles": 0.009, "Maatwebsite\\Excel\\Tests\\Concerns\\WithTitleTest::can_export_with_title": 0.007, "Maatwebsite\\Excel\\Tests\\Concerns\\WithTitleTest::can_export_sheet_title_when_longer_than_max_length": 0.006, "Maatwebsite\\Excel\\Tests\\DelegatedMacroableTest::can_call_methods_from_delegate": 0.004, "Maatwebsite\\Excel\\Tests\\DelegatedMacroableTest::can_use_writer_macros": 0.004, "Maatwebsite\\Excel\\Tests\\DelegatedMacroableTest::can_use_sheet_macros": 0.004, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::can_fake_an_export": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::can_assert_against_a_fake_downloaded_export": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::can_assert_against_a_fake_stored_export": 0, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::can_assert_regex_against_a_fake_stored_export_with_multiple_files": 0, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::a_callback_can_be_passed_as_the_second_argument_when_asserting_against_a_faked_stored_export": 0, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::can_assert_against_a_fake_queued_export": 0, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::can_assert_against_a_fake_implicitly_queued_export": 0, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::can_assert_against_a_fake_queued_export_with_chain": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::can_assert_against_a_fake_raw_export": 0, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::can_assert_against_a_fake_import": 0, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::can_assert_against_a_fake_import_with_uploaded_file": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::can_assert_against_a_fake_queued_import": 0, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::can_assert_against_a_fake_implicitly_queued_import": 0, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::can_assert_against_a_fake_queued_import_with_chain": 0, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::a_callback_can_be_passed_as_the_second_argument_when_asserting_against_a_faked_queued_export": 0, "Maatwebsite\\Excel\\Tests\\ExcelServiceProviderTest::custom_transaction_handler_is_bound": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelServiceProviderTest::is_bound": 0, "Maatwebsite\\Excel\\Tests\\ExcelServiceProviderTest::has_aliased": 0, "Maatwebsite\\Excel\\Tests\\ExcelServiceProviderTest::registers_console_commands": 0.006, "Maatwebsite\\Excel\\Tests\\ExcelServiceProviderTest::sets_php_spreadsheet_settings": 0, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_download_an_export_object_with_facade": 0.004, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_download_an_export_object": 0.004, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_store_an_export_object_on_default_disk": 0.004, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_store_an_export_object_on_another_disk": 0.004, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_store_csv_export_with_default_settings": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_get_raw_export_contents": 0.004, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_store_tsv_export_with_default_settings": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_store_csv_export_with_custom_settings": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelTest::cannot_use_from_collection_and_from_view_on_same_export": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_import_a_simple_xlsx_file_to_array": 0.003, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_import_a_simple_xlsx_file_to_collection": 0.039, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_import_a_simple_xlsx_file_to_collection_without_import_object": 0.031, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_import_a_simple_xlsx_file": 0.004, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_import_a_tsv_file": 0.005, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_chain_imports": 0.006, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_import_a_simple_xlsx_file_from_uploaded_file": 0.004, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_import_a_simple_xlsx_file_from_real_path": 0.003, "Maatwebsite\\Excel\\Tests\\ExcelTest::import_will_throw_error_when_no_reader_type_could_be_detected_when_no_extension": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelTest::import_will_throw_error_when_no_reader_type_could_be_detected_with_unknown_extension": 0.013, "Maatwebsite\\Excel\\Tests\\ExcelTest::can_import_without_extension_with_explicit_reader_type": 0.004, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::can_import_only_heading_row": 0.059, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::can_import_only_heading_row_with_custom_heading_row_formatter": 0.003, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::can_import_only_heading_row_with_custom_heading_row_formatter_with_key": 0.003, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::can_import_only_heading_row_with_custom_row_number": 0.008, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::can_import_only_heading_row_for_multiple_sheets": 0.003, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::can_import_only_heading_row_for_multiple_sheets_with_key": 0.003, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::can_import_only_heading_row_for_multiple_sheets_with_custom_row_number": 0.003, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::can_import_heading_row_with_custom_formatter_defined_in_config": 0.003, "Maatwebsite\\Excel\\Tests\\InteractsWithQueueTest::read_chunk_job_can_interact_with_queue": 0.001, "Maatwebsite\\Excel\\Tests\\InteractsWithQueueTest::append_data_to_sheet_job_can_interact_with_queue": 0, "Maatwebsite\\Excel\\Tests\\InteractsWithQueueTest::append_query_to_sheet_job_can_interact_with_queue": 0, "Maatwebsite\\Excel\\Tests\\InteractsWithQueueTest::append_view_to_sheet_job_can_interact_with_queue": 0, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadCollectionTest::can_download_a_collection_as_excel": 0.008, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadCollectionTest::can_download_a_collection_with_headers_as_excel": 0.009, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadCollectionTest::can_download_collection_with_headers_with_hidden_eloquent_attributes": 0.007, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadCollectionTest::can_download_collection_with_headers_when_making_attributes_visible": 0.007, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadCollectionTest::can_set_custom_response_headers": 0.005, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreCollectionTest::can_store_a_collection_as_excel": 0.011, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreCollectionTest::can_store_a_collection_as_excel_on_non_default_disk": 0.006, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreCollectionTest::can_store_a_collection_with_headings_as_excel": 0.007, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreCollectionTest::can_store_a_model_collection_with_headings_as_excel": 0.008, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_queue_an_export_and_store_on_different_disk": 0.234, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_queue_export_with_remote_temp_disk": 0.276, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_queue_export_with_remote_temp_disk_and_prefix": 0.24, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_implicitly_queue_an_export": 0.241, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_queue_export_with_mapping_on_eloquent_models": 0.017, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_catch_failures": 0.007, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_catch_failures_on_queue_export_job": 0.003, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_set_locale_on_queue_export_job": 0.015, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::can_queue_export_not_flushing_the_cache": 0.294, "Maatwebsite\\Excel\\Tests\\TemporaryFileTest::can_use_default_rights": 0.001, "Maatwebsite\\Excel\\Tests\\TemporaryFileTest::can_use_dir_rights": 0.001, "Maatwebsite\\Excel\\Tests\\TemporaryFileTest::can_use_file_rights": 0.001, "ShouldQueueWithoutChainTest::can_import_to_model_in_chunks": 0.065, "ShouldQueueWithoutChainTest::can_import_to_model_without_job_chaining": 0.053, "ShouldQueueWithoutChainTest::a_queue_name_can_be_specified_when_importing": 0.045, "ShouldQueueWithoutChainTest::the_cleanup_only_runs_when_all_jobs_are_done": 0.067, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnErrorTest::can_skip_on_error": 0.039, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnErrorTest::can_skip_errors_and_collect_all_errors_at_the_end": 0.036, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::can_skip_on_error": 0.037, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::skips_only_failed_rows_in_batch": 0.029, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::can_skip_failures_and_collect_all_failures_at_the_end": 0.028, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::can_validate_using_oneachrow_and_skipsonfailure": 0.028, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::can_validate_using_tocollection_and_skipsonfailure": 0.028, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::can_import_each_row_to_model": 0.058, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::has_timestamps_when_imported_single_model": 0.052, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::can_import_multiple_models_in_single_to_model": 0.06, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::can_import_multiple_different_types_of_models_in_single_to_model": 0.063, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBatchInsertsTest::can_import_to_model_in_batches": 0.074, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBatchInsertsTest::can_import_to_model_in_batches_bigger_file": 0.278, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBatchInsertsTest::can_import_multiple_different_types_of_models_in_single_to_model": 0.054, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBatchInsertsTest::has_timestamps_when_imported_in_batches": 0.056, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_to_model_in_chunks_un": 0.065, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_to_model_in_chunks_and_insert_in_batches": 0.436, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_to_model_in_chunks_and_insert_in_batches_with_heading_row": 0.429, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_csv_in_chunks_and_insert_in_batches": 1.206, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_to_model_in_chunks_and_insert_in_batches_with_multiple_sheets": 1.223, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_to_array_in_chunks": 1.854, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_to_model_in_chunks_and_insert_in_batches_with_multiple_sheets_objects_by_index": 1.216, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_to_model_in_chunks_and_insert_in_batches_with_multiple_sheets_objects_by_name": 1.211, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_catch_job_failed_in_chunks": 0.048, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_to_array_and_format_in_chunks": 0.054, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::can_import_to_array_in_chunks_without_formatting": 0.048, "Maatwebsite\\Excel\\Tests\\Concerns\\WithColumnLimitTest::can_import_to_array_with_column_limit": 0.026, "Maatwebsite\\Excel\\Tests\\Concerns\\WithColumnLimitTest::can_import_to_array_with_column_limit_and_skips_empty_rows": 0.033, "Maatwebsite\\Excel\\Tests\\Concerns\\WithGroupedHeadingRowTest::can_import_to_array_with_grouped_headers": 0.055, "Maatwebsite\\Excel\\Tests\\Concerns\\WithGroupedHeadingRowTest::can_import_oneachrow_with_grouped_headers": 0.053, "Maatwebsite\\Excel\\Tests\\Concerns\\WithGroupedHeadingRowTest::can_import_to_collection_with_grouped_headers": 0.05, "Maatwebsite\\Excel\\Tests\\Concerns\\WithGroupedHeadingRowTest::can_import_each_row_to_model_with_grouped_headers": 0.061, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::can_import_each_row_to_model_with_heading_row": 0.029, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::can_import_each_row_to_model_with_different_heading_row": 0.03, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::can_import_to_array_with_heading_row": 0.028, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::can_import_empty_rows_with_header": 0.029, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::can_import_empty_models_with_header": 0.03, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::can_cast_empty_headers_to_indexed_int": 0.028, "Maatwebsite\\Excel\\Tests\\Concerns\\WithLimitTest::can_import_a_limited_section_of_rows_to_model_with_different_start_row": 0.057, "Maatwebsite\\Excel\\Tests\\Concerns\\WithLimitTest::can_import_to_array_with_limit": 0.03, "Maatwebsite\\Excel\\Tests\\Concerns\\WithLimitTest::can_set_limit_bigger_than_row_size": 0.028, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappedCellsTest::can_import_with_references_to_cells": 0.034, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappedCellsTest::can_import_with_nested_references_to_cells": 0.031, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappedCellsTest::can_import_with_references_to_cells_to_model": 0.041, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStartRowTest::can_import_each_row_to_model_with_different_start_row": 0.027, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStartRowTest::can_import_to_array_with_start_row": 0.027, "Maatwebsite\\Excel\\Tests\\Concerns\\WithUpsertsTest::can_upsert_models_in_batches": 0.03, "Maatwebsite\\Excel\\Tests\\Concerns\\WithUpsertsTest::can_upsert_models_in_rows": 0.03, "Maatwebsite\\Excel\\Tests\\Concerns\\WithUpsertsTest::can_upsert_models_in_batches_with_defined_upsert_columns": 0.03, "Maatwebsite\\Excel\\Tests\\Concerns\\WithUpsertsTest::can_upsert_models_in_rows_with_defined_upsert_columns": 0.029, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_rows": 0.068, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_rows_with_closure_validation_rules": 0.054, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_rows_with_custom_validation_rule_objects": 0.07, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_rows_with_conditionality": 0.053, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_rows_with_unless_conditionality": 0.056, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_rows_with_combined_rules_with_colons": 0.067, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_with_custom_attributes": 0.049, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_with_custom_message": 0.051, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_rows_with_headings": 0.049, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_rows_with_grouped_headings": 0.049, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_rows_in_batches": 0.052, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_using_oneachrow": 0.05, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_using_collection": 0.053, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_validate_using_array": 0.129, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_configure_validator": 0.059, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_prepare_using_toarray": 0.057, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_prepare_using_tocollection": 0.051, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_prepare_using_tomodel": 0.053, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_prepare_using_oneachrow": 0.052, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::can_prepare_using_skipsemptyrows": 0.06, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::cannot_queue_import_that_does_not_implement_should_queue": 0.053, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_queue_an_import": 2.07, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_queue_an_import_with_batch_cache_and_file_store": 2.129, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_queue_import_with_remote_temp_disk": 2.209, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_keep_extension_for_temp_file_on_remote_disk": 2.125, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_queue_import_with_remote_temp_disk_and_prefix": 2.08, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_automatically_delete_temp_file_on_failure_when_using_remote_disk": 0.106, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::cannot_automatically_delete_temp_file_on_failure_when_using_local_disk": 0.12, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_force_remote_download_and_deletion_for_each_chunk_on_queue": 2.13, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_define_middleware_method_on_queued_import": 0.065, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_define_retry_until_method_on_queued_import": 0.064, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::can_define_max_exceptions_property_on_queued_import": 0.105, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_query": 0.116, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_relation_query_queued": 0.181, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_query_with_eager_loads": 0.085, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_query_with_eager_loads_and_queued": 0.106, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_query_builder_without_using_eloquent": 0.063, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_query_builder_without_using_eloquent_and_queued": 0.149, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_query_builder_with_nested_arrays": 0.069, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_query_builder_with_nested_arrays_queued": 0.079, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_query_with_batch_caching": 0.114, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_query_with_prepare_rows": 0.123, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::can_export_from_scout": 0.117, "Maatwebsite\\Excel\\Tests\\Concerns\\FromViewTest::can_export_from_view": 0.041, "Maatwebsite\\Excel\\Tests\\Concerns\\FromViewTest::can_export_multiple_sheets_from_view": 0.094, "Maatwebsite\\Excel\\Tests\\Concerns\\WithConditionalSheetsTest::can_select_which_sheets_will_be_imported": 0.013, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomQuerySizeTest::can_export_with_custom_count": 0.09, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::can_export_with_multiple_sheets_using_collections": 0.151, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::can_export_multiple_sheets_from_view": 0.11, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::unknown_sheet_index_will_throw_sheet_not_found_exception": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::unknown_sheet_name_will_throw_sheet_not_found_exception": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::unknown_sheet_name_can_be_ignored": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::unknown_sheet_indices_can_be_ignored_per_name": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::unknown_sheet_indices_can_be_ignored": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::unknown_sheet_indices_can_be_ignored_per_sheet": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::can_import_multiple_sheets": 0.013, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::can_import_multiple_sheets_by_sheet_name": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::can_import_multiple_sheets_by_sheet_index_and_name": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::can_import_multiple_sheets_by_sheet_name_and_index": 0.004, "Maatwebsite\\Excel\\Tests\\QueuedQueryExportTest::can_queue_an_export": 0.208, "Maatwebsite\\Excel\\Tests\\QueuedQueryExportTest::can_queue_an_export_with_batch_cache_and_file_store": 0.262, "Maatwebsite\\Excel\\Tests\\QueuedQueryExportTest::can_queue_an_export_with_mapping": 0.049, "Maatwebsite\\Excel\\Tests\\QueuedQueryExportTest::can_queue_scout_export": 0.228, "Maatwebsite\\Excel\\Tests\\QueuedViewExportTest::can_queue_an_export": 0.145, "Maatwebsite\\Excel\\Tests\\QueuedViewExportTest::can_export_multiple_sheets_from_view": 0.26, "Maatwebsite\\Excel\\Tests\\Concerns\\FromCollectionTest::can_export_from_lazy_collection_with_queue": 0.026, "Maatwebsite\\Excel\\Tests\\CellTest::can_get_cell_value": 0.029, "Maatwebsite\\Excel\\Tests\\CellTest::can_trim_empty_cells": 0.004, "Maatwebsite\\Excel\\Tests\\CellTest::convert_empty_cells_to_null": 0.003, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::export_chunked_events_get_called": 0.113, "Maatwebsite\\Excel\\Tests\\Mixins\\ImportAsMacroTest::can_import_directly_into_a_model_with_mapping": 0.036, "Maatwebsite\\Excel\\Tests\\Mixins\\ImportMacroTest::can_import_directly_into_a_model": 0.036, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadQueryMacroTest::can_download_a_query_as_excel": 0.062, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadQueryMacroTest::can_download_a_collection_with_headers_as_excel": 0.061, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreQueryMacroTest::can_download_a_query_as_excel": 0.063, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreQueryMacroTest::can_download_a_query_as_excel_on_different_disk": 0.061, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreQueryMacroTest::can_store_a_query_with_headers_as_excel": 0.07, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::can_import_models_with_belongs_to_relations": 0.067, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::can_import_models_with_belongs_to_many_relations": 0.07, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::test_can_import_only_heading_row": 0.005, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::test_will_get_multiple_from_memory_if_cells_hold_in_memory": 0.032, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::test_will_get_multiple_from_cache_if_cells_are_persisted": 0.001, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::test_will_get_multiple_from_cache_and_persisted": 0.001, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::test_it_persists_to_cache_when_memory_limit_reached_on_setting_a_value": 0.001, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::test_it_persists_to_cache_when_memory_limit_reached_on_setting_multiple_values": 0.001, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::test_it_writes_to_cache_with_default_ttl#null (forever)": 0.002, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::test_it_writes_to_cache_with_default_ttl#int value": 0.001, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::test_it_writes_to_cache_with_default_ttl#callable": 0.001, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::test_it_writes_to_cache_with_a_dateinterval_ttl": 0.001, "Maatwebsite\\Excel\\Tests\\Cache\\BatchCacheTest::test_it_can_override_default_ttl": 0, "Maatwebsite\\Excel\\Tests\\CellTest::test_can_get_cell_value": 0.043, "Maatwebsite\\Excel\\Tests\\CellTest::test_can_trim_empty_cells": 0.004, "Maatwebsite\\Excel\\Tests\\CellTest::test_convert_empty_cells_to_null": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::test_needs_to_have_a_file_name_when_downloading": 0.001, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::test_needs_to_have_a_file_name_when_storing": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::test_needs_to_have_a_file_name_when_queuing": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::test_responsable_needs_to_have_file_name_configured_inside_the_export": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::test_is_responsable": 0.037, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::test_can_have_customized_header": 0.002, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::test_can_set_custom_headers_in_export_class": 0.002, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::test_can_get_raw_export_contents": 0.008, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::test_can_have_customized_disk_options_when_storing": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::test_can_have_customized_disk_options_when_queueing": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::test_can_set_disk_options_in_export_class_when_storing": 0.001, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::test_can_set_disk_options_in_export_class_when_queuing": 0.001, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::test_can_override_export_class_disk_options_when_calling_store": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::test_can_override_export_class_disk_options_when_calling_queue": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::test_can_have_empty_disk_options_when_storing": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\ExportableTest::test_can_have_empty_disk_options_when_queueing": 0.001, "Maatwebsite\\Excel\\Tests\\Concerns\\FromArrayTest::test_can_export_from_array": 0.029, "Maatwebsite\\Excel\\Tests\\Concerns\\FromCollectionTest::test_can_export_from_collection": 0.049, "Maatwebsite\\Excel\\Tests\\Concerns\\FromCollectionTest::test_can_export_with_multiple_sheets_from_collection": 0.196, "Maatwebsite\\Excel\\Tests\\Concerns\\FromCollectionTest::test_can_export_from_lazy_collection": 0.01, "Maatwebsite\\Excel\\Tests\\Concerns\\FromCollectionTest::test_can_export_from_lazy_collection_with_queue": 0.04, "Maatwebsite\\Excel\\Tests\\Concerns\\FromGeneratorTest::test_can_export_from_generator": 0.01, "Maatwebsite\\Excel\\Tests\\Concerns\\FromIteratorTest::test_can_export_from_iterator": 0.01, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_query": 0.115, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_relation_query_queued": 0.183, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_query_with_eager_loads": 0.085, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_query_with_eager_loads_and_queued": 0.106, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_query_builder_without_using_eloquent": 0.07, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_query_builder_without_using_eloquent_and_queued": 0.149, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_query_builder_with_nested_arrays": 0.07, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_query_builder_with_nested_arrays_queued": 0.09, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_query_with_batch_caching": 0.116, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_query_with_prepare_rows": 0.117, "Maatwebsite\\Excel\\Tests\\Concerns\\FromQueryTest::test_can_export_from_scout": 0.144, "Maatwebsite\\Excel\\Tests\\Concerns\\FromViewTest::test_can_export_from_view": 0.04, "Maatwebsite\\Excel\\Tests\\Concerns\\FromViewTest::test_can_export_multiple_sheets_from_view": 0.101, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::test_can_import_a_simple_xlsx_file": 0.008, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::test_can_import_a_simple_xlsx_file_from_uploaded_file": 0.01, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::test_can_import_a_simple_csv_file_with_html_tags_inside": 0.011, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::test_can_import_a_simple_xlsx_file_with_ignore_empty_set_to_true": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::test_can_import_a_simple_xlsx_file_with_ignore_empty_set_to_false": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\OnEachRowTest::test_can_import_each_row_individually": 0.007, "Maatwebsite\\Excel\\Tests\\Concerns\\OnEachRowTest::test_it_respects_the_end_column": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\RegistersEventListenersTest::test_events_get_called_when_exporting": 0.016, "Maatwebsite\\Excel\\Tests\\Concerns\\RegistersEventListenersTest::test_events_get_called_when_importing": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\RegistersEventListenersTest::test_can_have_invokable_class_as_listener": 0.01, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersChunkOffsetTest::test_can_set_and_get_chunk_offset": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersChunkOffsetTest::test_can_access_chunk_offset_on_import_to_array_in_chunks": 0.121, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersRowNumberTest::test_can_set_and_get_row_number": 0, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersRowNumberTest::test_can_access_row_number_on_import_to_model": 0.145, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersRowNumberTest::test_can_access_row_number_on_import_to_array_in_chunks": 0.086, "Maatwebsite\\Excel\\Tests\\Concerns\\RemembersRowNumberTest::test_can_access_row_number_on_import_to_array_in_chunks_with_batch_inserts": 0.078, "ShouldQueueWithoutChainTest::test_can_import_to_model_in_chunks": 0.065, "ShouldQueueWithoutChainTest::test_can_import_to_model_without_job_chaining": 0.046, "ShouldQueueWithoutChainTest::test_a_queue_name_can_be_specified_when_importing": 0.051, "ShouldQueueWithoutChainTest::test_the_cleanup_only_runs_when_all_jobs_are_done": 0.061, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::test_skips_empty_rows_when_importing_to_collection": 0.006, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::test_skips_empty_rows_when_importing_on_each_row": 0.003, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::test_skips_empty_rows_when_importing_to_model": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::test_custom_skips_rows_when_importing_to_collection": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::test_custom_skips_rows_when_importing_to_model": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsEmptyRowsTest::test_custom_skips_rows_when_using_oneachrow": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnErrorTest::test_can_skip_on_error": 0.042, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnErrorTest::test_can_skip_errors_and_collect_all_errors_at_the_end": 0.037, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::test_can_skip_on_error": 0.038, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::test_skips_only_failed_rows_in_batch": 0.027, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::test_can_skip_failures_and_collect_all_failures_at_the_end": 0.028, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::test_can_validate_using_oneachrow_and_skipsonfailure": 0.028, "Maatwebsite\\Excel\\Tests\\Concerns\\SkipsOnFailureTest::test_can_validate_using_tocollection_and_skipsonfailure": 0.028, "Maatwebsite\\Excel\\Tests\\Concerns\\ToArrayTest::test_can_import_to_array": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\ToArrayTest::test_can_import_multiple_sheets_to_array": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\ToCollectionTest::test_can_import_to_collection": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\ToCollectionTest::test_can_import_multiple_sheets_to_collection": 0.003, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::test_can_import_each_row_to_model": 0.064, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::test_has_timestamps_when_imported_single_model": 0.055, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::test_can_import_multiple_models_in_single_to_model": 0.059, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::test_can_import_multiple_different_types_of_models_in_single_to_model": 0.055, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::test_can_import_models_with_belongs_to_relations": 0.068, "Maatwebsite\\Excel\\Tests\\Concerns\\ToModelTest::test_can_import_models_with_belongs_to_many_relations": 0.076, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBackgroundColorTest::test_can_configure_background_color_from_rgb_string": 0.017, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBackgroundColorTest::test_can_configure_background_color_as_array": 0.009, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBackgroundColorTest::test_can_configure_background_color_with_color_instance": 0.011, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBatchInsertsTest::test_can_import_to_model_in_batches": 0.059, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBatchInsertsTest::test_can_import_to_model_in_batches_bigger_file": 0.278, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBatchInsertsTest::test_can_import_multiple_different_types_of_models_in_single_to_model": 0.054, "Maatwebsite\\Excel\\Tests\\Concerns\\WithBatchInsertsTest::test_has_timestamps_when_imported_in_batches": 0.058, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::test_by_default_does_not_calculate_formulas": 0.007, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::test_can_import_to_array_with_calculated_formulas": 0.006, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::test_can_import_to_model_with_calculated_formulas": 0.006, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::test_can_import_to_array_with_calculated_formulas_and_multi_sheet_references": 0.009, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::test_can_import_to_array_with_calculated_formulas_and_skips_empty": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCalculatedFormulasTest::test_can_import_to_model_with_calculated_formulas_and_skips_empty": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_to_model_in_chunks_un": 0.064, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_to_model_in_chunks_and_insert_in_batches": 0.438, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_to_model_in_chunks_and_insert_in_batches_with_heading_row": 0.441, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_csv_in_chunks_and_insert_in_batches": 1.218, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_to_model_in_chunks_and_insert_in_batches_with_multiple_sheets": 1.254, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_to_array_in_chunks": 1.882, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_to_model_in_chunks_and_insert_in_batches_with_multiple_sheets_objects_by_index": 1.222, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_to_model_in_chunks_and_insert_in_batches_with_multiple_sheets_objects_by_name": 1.219, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_catch_job_failed_in_chunks": 0.048, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_to_array_and_format_in_chunks": 0.051, "Maatwebsite\\Excel\\Tests\\Concerns\\WithChunkReadingTest::test_can_import_to_array_in_chunks_without_formatting": 0.088, "Maatwebsite\\Excel\\Tests\\Concerns\\WithColumnFormattingTest::test_can_export_with_column_formatting": 0.018, "Maatwebsite\\Excel\\Tests\\Concerns\\WithColumnLimitTest::test_can_import_to_array_with_column_limit": 0.026, "Maatwebsite\\Excel\\Tests\\Concerns\\WithColumnLimitTest::test_can_import_to_array_with_column_limit_and_skips_empty_rows": 0.032, "Maatwebsite\\Excel\\Tests\\Concerns\\WithColumnWidthsTest::test_can_set_column_width": 0.016, "Maatwebsite\\Excel\\Tests\\Concerns\\WithConditionalSheetsTest::test_can_select_which_sheets_will_be_imported": 0.013, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomCsvSettingsTest::test_can_store_csv_export_with_custom_settings": 0.003, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomCsvSettingsTest::test_can_store_csv_export_with_custom_encoding": 0.003, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomCsvSettingsTest::test_can_read_csv_with_auto_detecting_delimiter_semicolon": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomCsvSettingsTest::test_can_read_csv_with_auto_detecting_delimiter_comma": 0.003, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomCsvSettingsTest::test_can_read_csv_import_with_custom_settings": 0.003, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomCsvSettingsTest::test_cannot_read_with_wrong_delimiter": 0.003, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomQuerySizeTest::test_can_export_with_custom_count": 0.09, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomStartCellTest::test_can_store_collection_with_custom_start_cell": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomValueBinderTest::test_can_set_a_value_binder_on_export": 0.017, "Maatwebsite\\Excel\\Tests\\Concerns\\WithCustomValueBinderTest::test_can_set_a_value_binder_on_import": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\WithDefaultStylesTest::test_can_configure_default_styles": 0.015, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::test_export_events_get_called": 0.011, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::test_import_events_get_called": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::test_import_chunked_events_get_called": 0.093, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::test_can_have_invokable_class_as_listener": 0.01, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::test_can_have_global_event_listeners": 0.009, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::test_can_have_custom_concern_handlers": 0.036, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::test_can_have_custom_sheet_concern_handlers": 0.019, "Maatwebsite\\Excel\\Tests\\Concerns\\WithEventsTest::test_export_chunked_events_get_called": 0.002, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::test_by_default_import_to_array": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::test_can_import_to_array_with_format_data": 0.006, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::test_can_import_to_array_with_format_data_and_skips_empty_rows": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::test_by_default_import_to_collection": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::test_can_import_to_collection_with_format_data": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::test_by_default_import_to_model": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\WithFormatDataTest::test_can_import_to_model_with_format_data": 0.005, "Maatwebsite\\Excel\\Tests\\Concerns\\WithGroupedHeadingRowTest::test_can_import_to_array_with_grouped_headers": 0.049, "Maatwebsite\\Excel\\Tests\\Concerns\\WithGroupedHeadingRowTest::test_can_import_oneachrow_with_grouped_headers": 0.047, "Maatwebsite\\Excel\\Tests\\Concerns\\WithGroupedHeadingRowTest::test_can_import_to_collection_with_grouped_headers": 0.049, "Maatwebsite\\Excel\\Tests\\Concerns\\WithGroupedHeadingRowTest::test_can_import_each_row_to_model_with_grouped_headers": 0.068, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::test_can_import_each_row_to_model_with_heading_row": 0.029, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::test_can_import_each_row_to_model_with_different_heading_row": 0.032, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::test_can_import_to_array_with_heading_row": 0.026, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::test_can_import_empty_rows_with_header": 0.03, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::test_can_import_empty_models_with_header": 0.029, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingRowTest::test_can_cast_empty_headers_to_indexed_int": 0.025, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingsTest::test_can_export_from_collection_with_heading_row": 0.017, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingsTest::test_can_export_from_collection_with_multiple_heading_rows": 0.011, "Maatwebsite\\Excel\\Tests\\Concerns\\WithHeadingsTest::test_can_export_from_collection_with_heading_row_with_custom_start_cell": 0.01, "Maatwebsite\\Excel\\Tests\\Concerns\\WithLimitTest::test_can_import_a_limited_section_of_rows_to_model_with_different_start_row": 0.033, "Maatwebsite\\Excel\\Tests\\Concerns\\WithLimitTest::test_can_import_to_array_with_limit": 0.026, "Maatwebsite\\Excel\\Tests\\Concerns\\WithLimitTest::test_can_set_limit_bigger_than_row_size": 0.025, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappedCellsTest::test_can_import_with_references_to_cells": 0.026, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappedCellsTest::test_can_import_with_nested_references_to_cells": 0.026, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappedCellsTest::test_can_import_with_references_to_cells_to_model": 0.028, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappingTest::test_can_export_with_heading": 0.018, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappingTest::test_can_return_multiple_rows_in_map": 0.017, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMappingTest::test_json_array_columns_shouldnt_be_detected_as_multiple_rows": 0.011, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_can_export_with_multiple_sheets_using_collections": 0.143, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_can_export_multiple_sheets_from_view": 0.11, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_unknown_sheet_index_will_throw_sheet_not_found_exception": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_unknown_sheet_name_will_throw_sheet_not_found_exception": 0.003, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_unknown_sheet_name_can_be_ignored": 0.003, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_unknown_sheet_indices_can_be_ignored_per_name": 0.003, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_unknown_sheet_indices_can_be_ignored": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_unknown_sheet_indices_can_be_ignored_per_sheet": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_can_import_multiple_sheets": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_can_import_multiple_sheets_by_sheet_name": 0.013, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_can_import_multiple_sheets_by_sheet_index_and_name": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithMultipleSheetsTest::test_can_import_multiple_sheets_by_sheet_name_and_index": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithPropertiesTest::test_can_set_custom_document_properties": 0.009, "Maatwebsite\\Excel\\Tests\\Concerns\\WithPropertiesTest::test_it_merges_with_default_properties": 0.009, "Maatwebsite\\Excel\\Tests\\Concerns\\WithPropertiesTest::test_it_ignores_empty_properties": 0.011, "Maatwebsite\\Excel\\Tests\\Concerns\\WithReadFilterTest::test_can_register_custom_read_filter": 0.006, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStartRowTest::test_can_import_each_row_to_model_with_different_start_row": 0.027, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStartRowTest::test_can_import_to_array_with_start_row": 0.04, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStrictNullComparisonTest::test_exported_zero_values_are_not_null_when_exporting_with_strict_null_comparison": 0.015, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStrictNullComparisonTest::test_exported_zero_values_are_null_when_not_exporting_with_strict_null_comparison": 0.012, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStrictNullComparisonTest::test_exports_trailing_empty_cells": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStrictNullComparisonTest::test_exports_trailing_empty_cells_by_setting_config_strict_null_comparison": 0.004, "Maatwebsite\\Excel\\Tests\\Concerns\\WithStylesTest::test_can_configure_styles": 0.017, "Maatwebsite\\Excel\\Tests\\Concerns\\WithTitleTest::test_can_export_with_title": 0.01, "Maatwebsite\\Excel\\Tests\\Concerns\\WithTitleTest::test_can_export_sheet_title_when_longer_than_max_length": 0.011, "Maatwebsite\\Excel\\Tests\\Concerns\\WithUpsertsTest::test_can_upsert_models_in_batches": 0.029, "Maatwebsite\\Excel\\Tests\\Concerns\\WithUpsertsTest::test_can_upsert_models_in_rows": 0.041, "Maatwebsite\\Excel\\Tests\\Concerns\\WithUpsertsTest::test_can_upsert_models_in_batches_with_defined_upsert_columns": 0.029, "Maatwebsite\\Excel\\Tests\\Concerns\\WithUpsertsTest::test_can_upsert_models_in_rows_with_defined_upsert_columns": 0.028, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_rows": 0.051, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_rows_with_closure_validation_rules": 0.051, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_rows_with_custom_validation_rule_objects": 0.052, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_rows_with_conditionality": 0.049, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_rows_with_unless_conditionality": 0.052, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_rows_with_combined_rules_with_colons": 0.069, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_with_custom_attributes": 0.049, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_with_custom_message": 0.051, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_rows_with_headings": 0.055, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_rows_with_grouped_headings": 0.049, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_rows_in_batches": 0.05, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_using_oneachrow": 0.051, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_using_collection": 0.051, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_validate_using_array": 0.048, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_configure_validator": 0.057, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_prepare_using_toarray": 0.05, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_prepare_using_tocollection": 0.051, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_prepare_using_tomodel": 0.051, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_prepare_using_oneachrow": 0.05, "Maatwebsite\\Excel\\Tests\\Concerns\\WithValidationTest::test_can_prepare_using_skipsemptyrows": 0.049, "Maatwebsite\\Excel\\Tests\\DelegatedMacroableTest::test_can_call_methods_from_delegate": 0.009, "Maatwebsite\\Excel\\Tests\\DelegatedMacroableTest::test_can_use_writer_macros": 0.01, "Maatwebsite\\Excel\\Tests\\DelegatedMacroableTest::test_can_use_sheet_macros": 0.008, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::test_can_fake_an_export": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::test_can_assert_against_a_fake_downloaded_export": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::test_can_assert_against_a_fake_stored_export": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::test_can_assert_regex_against_a_fake_stored_export_with_multiple_files": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::test_a_callback_can_be_passed_as_the_second_argument_when_asserting_against_a_faked_stored_export": 0, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::test_can_assert_against_a_fake_queued_export": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::test_can_assert_against_a_fake_implicitly_queued_export": 0, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::test_can_assert_against_a_fake_queued_export_with_chain": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::test_can_assert_against_a_fake_raw_export": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::test_can_assert_against_a_fake_import": 0.004, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::test_can_assert_against_a_fake_import_with_uploaded_file": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::test_can_assert_against_a_fake_queued_import": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::test_can_assert_against_a_fake_implicitly_queued_import": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::test_can_assert_against_a_fake_queued_import_with_chain": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelFakeTest::test_a_callback_can_be_passed_as_the_second_argument_when_asserting_against_a_faked_queued_export": 0, "Maatwebsite\\Excel\\Tests\\ExcelServiceProviderTest::test_custom_transaction_handler_is_bound": 0, "Maatwebsite\\Excel\\Tests\\ExcelServiceProviderTest::test_is_bound": 0, "Maatwebsite\\Excel\\Tests\\ExcelServiceProviderTest::test_has_aliased": 0, "Maatwebsite\\Excel\\Tests\\ExcelServiceProviderTest::test_registers_console_commands": 0.008, "Maatwebsite\\Excel\\Tests\\ExcelServiceProviderTest::test_sets_php_spreadsheet_settings": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_download_an_export_object_with_facade": 0.008, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_download_an_export_object": 0.007, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_store_an_export_object_on_default_disk": 0.01, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_store_an_export_object_on_another_disk": 0.012, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_store_csv_export_with_default_settings": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_get_raw_export_contents": 0.012, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_store_tsv_export_with_default_settings": 0.002, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_store_csv_export_with_custom_settings": 0.002, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_cannot_use_from_collection_and_from_view_on_same_export": 0.002, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_import_a_simple_xlsx_file_to_array": 0.003, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_import_a_simple_xlsx_file_to_collection": 0.003, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_import_a_simple_xlsx_file_to_collection_without_import_object": 0.003, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_import_a_simple_xlsx_file": 0.003, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_import_a_tsv_file": 0.009, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_chain_imports": 0.003, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_import_a_simple_xlsx_file_from_uploaded_file": 0.005, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_import_a_simple_xlsx_file_from_real_path": 0.005, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_import_will_throw_error_when_no_reader_type_could_be_detected_when_no_extension": 0.001, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_import_will_throw_error_when_no_reader_type_could_be_detected_with_unknown_extension": 0.035, "Maatwebsite\\Excel\\Tests\\ExcelTest::test_can_import_without_extension_with_explicit_reader_type": 0.004, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::test_can_import_only_heading_row_with_custom_heading_row_formatter": 0.003, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::test_can_import_only_heading_row_with_custom_heading_row_formatter_with_key": 0.003, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::test_can_import_only_heading_row_with_custom_row_number": 0.005, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::test_can_import_only_heading_row_for_multiple_sheets": 0.003, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::test_can_import_only_heading_row_for_multiple_sheets_with_key": 0.005, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::test_can_import_only_heading_row_for_multiple_sheets_with_custom_row_number": 0.004, "Maatwebsite\\Excel\\Tests\\HeadingRowImportTest::test_can_import_heading_row_with_custom_formatter_defined_in_config": 0.004, "Maatwebsite\\Excel\\Tests\\InteractsWithQueueTest::test_read_chunk_job_can_interact_with_queue": 0.001, "Maatwebsite\\Excel\\Tests\\InteractsWithQueueTest::test_append_data_to_sheet_job_can_interact_with_queue": 0, "Maatwebsite\\Excel\\Tests\\InteractsWithQueueTest::test_append_query_to_sheet_job_can_interact_with_queue": 0.001, "Maatwebsite\\Excel\\Tests\\InteractsWithQueueTest::test_append_view_to_sheet_job_can_interact_with_queue": 0.001, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadCollectionTest::test_can_download_a_collection_as_excel": 0.011, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadCollectionTest::test_can_download_a_collection_with_headers_as_excel": 0.011, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadCollectionTest::test_can_download_collection_with_headers_with_hidden_eloquent_attributes": 0.011, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadCollectionTest::test_can_download_collection_with_headers_when_making_attributes_visible": 0.011, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadCollectionTest::test_can_set_custom_response_headers": 0.008, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadQueryMacroTest::test_can_download_a_query_as_excel": 0.058, "Maatwebsite\\Excel\\Tests\\Mixins\\DownloadQueryMacroTest::test_can_download_a_collection_with_headers_as_excel": 0.062, "Maatwebsite\\Excel\\Tests\\Mixins\\ImportAsMacroTest::test_can_import_directly_into_a_model_with_mapping": 0.032, "Maatwebsite\\Excel\\Tests\\Mixins\\ImportMacroTest::test_can_import_directly_into_a_model": 0.035, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreCollectionTest::test_can_store_a_collection_as_excel": 0.012, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreCollectionTest::test_can_store_a_collection_as_excel_on_non_default_disk": 0.01, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreCollectionTest::test_can_store_a_collection_with_headings_as_excel": 0.01, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreCollectionTest::test_can_store_a_model_collection_with_headings_as_excel": 0, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreQueryMacroTest::test_can_download_a_query_as_excel": 0.061, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreQueryMacroTest::test_can_download_a_query_as_excel_on_different_disk": 0.059, "Maatwebsite\\Excel\\Tests\\Mixins\\StoreQueryMacroTest::test_can_store_a_query_with_headers_as_excel": 0.065, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::test_can_queue_an_export": 0.365, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::test_can_queue_an_export_and_store_on_different_disk": 0.349, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::test_can_queue_export_with_remote_temp_disk": 0.444, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::test_can_queue_export_with_remote_temp_disk_and_prefix": 0.335, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::test_can_implicitly_queue_an_export": 0.332, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::test_can_queue_export_with_mapping_on_eloquent_models": 0.029, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::test_can_catch_failures": 0.01, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::test_can_catch_failures_on_queue_export_job": 0.003, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::test_can_set_locale_on_queue_export_job": 0.03, "Maatwebsite\\Excel\\Tests\\QueuedExportTest::test_can_queue_export_not_flushing_the_cache": 0.385, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_cannot_queue_import_that_does_not_implement_should_queue": 0.046, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_queue_an_import": 2.07, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_queue_an_import_with_batch_cache_and_file_store": 3.877, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_queue_import_with_remote_temp_disk": 2.219, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_keep_extension_for_temp_file_on_remote_disk": 2.262, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_queue_import_with_remote_temp_disk_and_prefix": 2.062, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_automatically_delete_temp_file_on_failure_when_using_remote_disk": 0.108, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_cannot_automatically_delete_temp_file_on_failure_when_using_local_disk": 0.106, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_force_remote_download_and_deletion_for_each_chunk_on_queue": 2.176, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_define_middleware_method_on_queued_import": 0.067, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_define_retry_until_method_on_queued_import": 0.064, "Maatwebsite\\Excel\\Tests\\QueuedImportTest::test_can_define_max_exceptions_property_on_queued_import": 0.106, "Maatwebsite\\Excel\\Tests\\QueuedQueryExportTest::test_can_queue_an_export": 0.205, "Maatwebsite\\Excel\\Tests\\QueuedQueryExportTest::test_can_queue_an_export_with_batch_cache_and_file_store": 3.691, "Maatwebsite\\Excel\\Tests\\QueuedQueryExportTest::test_can_queue_an_export_with_mapping": 0.051, "Maatwebsite\\Excel\\Tests\\QueuedQueryExportTest::test_can_queue_scout_export": 0.372, "Maatwebsite\\Excel\\Tests\\QueuedViewExportTest::test_can_queue_an_export": 0.138, "Maatwebsite\\Excel\\Tests\\QueuedViewExportTest::test_can_export_multiple_sheets_from_view": 0.266, "Maatwebsite\\Excel\\Tests\\TemporaryFileTest::test_can_use_default_rights": 0.002, "Maatwebsite\\Excel\\Tests\\TemporaryFileTest::test_can_use_dir_rights": 0.001, "Maatwebsite\\Excel\\Tests\\TemporaryFileTest::test_can_use_file_rights": 0.001, "Maatwebsite\\Excel\\Tests\\Concerns\\ImportableTest::test_cannot_import_a_non_existing_xlsx_file": 0.001, "Maatwebsite\\Excel\\Tests\\Validators\\RowValidatorTest::test_format_rule_with_array_input": 0.025, "Maatwebsite\\Excel\\Tests\\Validators\\RowValidatorTest::test_format_rule_with_object_input": 0, "Maatwebsite\\Excel\\Tests\\Validators\\RowValidatorTest::test_format_rule_with_callable_input": 0, "Maatwebsite\\Excel\\Tests\\Validators\\RowValidatorTest::test_format_rule_with_string_input_matching_pattern": 0.006, "Maatwebsite\\Excel\\Tests\\Validators\\RowValidatorTest::test_format_rule_with_string_input_not_matching_pattern": 0, "Maatwebsite\\Excel\\Tests\\Validators\\RowValidatorTest::test_format_rule_with_required_without_all": 0.006, "Maatwebsite\\Excel\\Tests\\Validators\\RowValidatorTest::test_format_rule_with_required_without": 0}}