URI.MungeResources
TYPE: bool
VERSION: 3.1.1
DEFAULT: false
--DESCRIPTION--
<p>
    If true, any URI munging directives like %URI.Munge
    will also apply to embedded resources, such as <code>&lt;img src=""&gt;</code>.
    Be careful enabling this directive if you have a redirector script
    that does not use the <code>Location</code> HTTP header; all of your images
    and other embedded resources will break.
</p>
<p>
    <strong>Warning:</strong> It is strongly advised you use this in conjunction
    %URI.MungeSecretKey to mitigate the security risk of an open redirector.
</p>
--# vim: et sw=4 sts=4
