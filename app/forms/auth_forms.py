from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, BooleanField, SubmitField, SelectField
from wtforms.validators import ValidationError, DataRequired, Email, EqualTo
from app.models.user import User

class LoginForm(FlaskForm):
    username = <PERSON><PERSON><PERSON>('Tên đăng nhập', validators=[DataRequired()])
    password = PasswordField('Mật khẩu', validators=[DataRequired()])
    remember_me = BooleanField('Ghi nhớ đăng nhập')
    submit = SubmitField('Đăng nhập')

class RegistrationForm(FlaskForm):
    username = StringField('Tên đăng nhập', validators=[DataRequired()])
    email = StringField('Email', validators=[DataRequired(), Email()])
    full_name = StringField('Họ và tên', validators=[DataRequired()])
    phone = StringField('<PERSON><PERSON> điện thoại')
    role = SelectField('<PERSON><PERSON> trò', choices=[
        ('admin', 'Admin'),
        ('manager', '<PERSON>u<PERSON><PERSON> sinh'),
        ('teacher', '<PERSON>i<PERSON><PERSON> viên')
    ], validators=[DataRequired()])
    password = PasswordField('Mật khẩu', validators=[DataRequired()])
    password2 = PasswordField('Nhập lại mật khẩu', 
                             validators=[DataRequired(), EqualTo('password')])
    submit = SubmitField('Đăng ký')

    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None:
            raise ValidationError('Tên đăng nhập đã tồn tại.')

    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is not None:
            raise ValidationError('Email đã được sử dụng.')
