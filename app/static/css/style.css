/* Modern styles for the classroom management system */

:root {
    --primary-color: #4f46e5;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    --light-color: #f8fafc;
    --dark-color: #1f2937;
    --border-radius: 12px;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    background-attachment: fixed;
}

/* Modern navbar */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--box-shadow);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color) !important;
}

/* Modern cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), #6366f1);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    border: none;
    font-weight: 600;
}

/* Modern buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #6366f1);
    box-shadow: 0 2px 4px rgba(79, 70, 229, 0.3);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(79, 70, 229, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #059669);
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color), #2563eb);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.feature-item {
    padding: 1.5rem;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--box-shadow-lg);
}

.btn-block {
    width: 100%;
}

/* Schedule table styles */
.schedule-table {
    font-size: 0.9rem;
}

.schedule-table th {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    text-align: center;
    vertical-align: middle;
    padding: 0.5rem;
}

.schedule-table td {
    border: 1px solid #dee2e6;
    padding: 0.25rem;
    vertical-align: top;
    height: 80px;
    position: relative;
}

.schedule-item {
    background-color: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 4px;
    padding: 4px;
    margin: 2px 0;
    font-size: 0.8rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.schedule-item:hover {
    background-color: #bbdefb;
}

.schedule-item .class-name {
    font-weight: bold;
    color: #1976d2;
}

.schedule-item .teacher-name {
    color: #666;
    font-size: 0.7rem;
}

.schedule-item .time-range {
    color: #888;
    font-size: 0.7rem;
}

/* Attendance styles */
.attendance-table {
    font-size: 0.9rem;
}

.attendance-status {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-present {
    background-color: #d4edda;
    color: #155724;
}

.status-absent-with-reason {
    background-color: #fff3cd;
    color: #856404;
}

.status-absent-without-reason {
    background-color: #f8d7da;
    color: #721c24;
}

/* Modal styles */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* Dashboard cards */
.dashboard-card {
    transition: transform 0.2s;
}

.dashboard-card:hover {
    transform: translateY(-2px);
}

/* Finance table */
.finance-income {
    color: #28a745;
    font-weight: bold;
}

.finance-expense {
    color: #dc3545;
    font-weight: bold;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .schedule-table {
        font-size: 0.8rem;
    }
    
    .schedule-table td {
        height: 60px;
    }
    
    .schedule-item {
        font-size: 0.7rem;
        padding: 2px;
    }
    
    .navbar-nav .dropdown-menu {
        position: static;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        box-shadow: none;
    }
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Form validation */
.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

/* Success/Error messages */
.alert {
    border-radius: 0.5rem;
}

/* Pagination */
.pagination {
    justify-content: center;
}

/* Table responsive */
.table-responsive {
    border-radius: 0.5rem;
}

/* Custom button styles */
.btn-outline-primary:hover,
.btn-outline-success:hover,
.btn-outline-info:hover,
.btn-outline-warning:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}
