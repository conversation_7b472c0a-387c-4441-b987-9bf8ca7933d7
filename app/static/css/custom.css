/* Vietnamese Classroom Management - Custom Styles */

/* Color Scheme - White Orange Theme */
:root {
    --primary-color: #ff6b35;
    --secondary-color: #ff8c42;
    --success-color: #28a745;
    --warning-color: #ffa726;
    --danger-color: #dc3545;
    --info-color: #29b6f6;
    --light-color: #ffffff;
    --dark-color: #2c3e50;
    --morning-color: #ff6b35;
    --afternoon-color: #ff8c42;
    --evening-color: #ffa726;
    --accent-color: #ffab91;
    --background-color: #fafafa;
}

/* General Improvements */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background-color);
    color: var(--dark-color);
}

.navbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    box-shadow: 0 2px 15px rgba(255, 107, 53, 0.2);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.3rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    border: none;
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: 25px;
    font-weight: 500;
    padding: 8px 20px;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.btn-success {
    background: linear-gradient(135deg, #4caf50 0%, #81c784 100%);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, var(--accent-color) 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #e53935 0%, #ef5350 100%);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #4fc3f7 100%);
}

/* Schedule Items */
.schedule-item {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 12px;
    border-radius: 12px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.schedule-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.schedule-item:hover::before {
    left: 100%;
}

.schedule-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.2);
}

.morning-session {
    background: linear-gradient(135deg, var(--morning-color) 0%, #4facfe 100%);
}

.afternoon-session {
    background: linear-gradient(135deg, var(--afternoon-color) 0%, #56ab2f 100%);
}

.evening-session {
    background: linear-gradient(135deg, var(--evening-color) 0%, #f093fb 100%);
}

/* Schedule Table */
.schedule-table {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.schedule-table th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    text-align: center;
    font-weight: 600;
    padding: 15px;
    border: none;
}

.schedule-table td {
    border: 1px solid #e9ecef;
    padding: 10px;
    vertical-align: top;
    background: white;
}

.schedule-cell {
    min-height: 120px;
    position: relative;
}

/* Calendar */
.calendar-cell {
    background: white;
    transition: background-color 0.3s ease;
}

.calendar-cell:hover {
    background-color: #f8f9fa;
}

/* Forms */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* Statistics Cards */
.stat-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
}

/* Alerts */
.alert {
    border-radius: 12px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* Tables */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr {
    transition: background-color 0.3s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Badges */
.badge {
    border-radius: 20px;
    padding: 6px 12px;
    font-weight: 500;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .card {
        margin-bottom: 15px;
    }
    
    .schedule-item {
        padding: 8px;
        font-size: 0.9rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .btn {
        padding: 6px 15px;
        font-size: 0.9rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e9ecef;
    }
    
    .card {
        background-color: #2d3748;
        color: #e9ecef;
    }
    
    .table tbody tr {
        background-color: #2d3748;
        color: #e9ecef;
    }
    
    .form-control {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e9ecef;
    }
}
