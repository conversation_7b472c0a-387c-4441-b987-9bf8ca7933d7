{% extends "base_tailwind.html" %}

{% block content %}
<!-- Header -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-tachometer-alt text-orange-500 mr-3"></i>
                Dashboard
            </h1>
            <p class="text-gray-600 mt-1">Chào mừng {{ current_user.full_name }}</p>
        </div>
        
        <div class="flex space-x-2">
            <a href="{{ url_for('user.weekly_schedule') }}" 
               class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-calendar-week mr-2"></i>
                Lị<PERSON> tuần
            </a>
            <a href="{{ url_for('user.view_finance') }}" 
               class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-chart-line mr-2"></i>
                Thu chi
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
    <div class="bg-white rounded-lg shadow-sm p-3 border-l-4 border-blue-500">
        <div class="flex items-center">
            <i class="fas fa-chalkboard text-blue-600 text-lg mr-2"></i>
            <div>
                <p class="text-xs text-gray-600">Tổng lớp</p>
                <p class="text-lg font-bold text-gray-900">{{ stats.total_classes }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm p-3 border-l-4 border-green-500">
        <div class="flex items-center">
            <i class="fas fa-users text-green-600 text-lg mr-2"></i>
            <div>
                <p class="text-xs text-gray-600">Học sinh</p>
                <p class="text-lg font-bold text-gray-900">{{ stats.total_students }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm p-3 border-l-4 border-purple-500">
        <div class="flex items-center">
            <i class="fas fa-calendar-day text-purple-600 text-lg mr-2"></i>
            <div>
                <p class="text-xs text-gray-600">Lịch hôm nay</p>
                <p class="text-lg font-bold text-gray-900">{{ stats.today_schedules }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm p-3 border-l-4 border-orange-500">
        <div class="flex items-center">
            <i class="fas fa-user-check text-orange-600 text-lg mr-2"></i>
            <div>
                <p class="text-xs text-gray-600">Điểm danh tháng</p>
                <p class="text-lg font-bold text-gray-900">{{ stats.this_month_attendance }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Today's Schedule -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-calendar-day text-blue-500 mr-2"></i>
            Lịch dạy hôm nay
        </h3>
        <a href="{{ url_for('user.weekly_schedule') }}" 
           class="text-orange-600 hover:text-orange-700 text-sm font-medium">
            Xem tất cả →
        </a>
    </div>
    
    {% if today_schedules %}
    <div class="space-y-3">
        {% for schedule in today_schedules %}
        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chalkboard-teacher text-orange-600"></i>
                </div>
                <div>
                    <div class="font-medium text-gray-900">{{ schedule.class_obj.name }}</div>
                    <div class="text-sm text-gray-500">{{ schedule.teacher.full_name }}</div>
                </div>
            </div>
            <div class="text-right">
                <div class="text-sm font-medium text-gray-900">
                    {{ schedule.start_time.strftime('%H:%M') }} - {{ schedule.end_time.strftime('%H:%M') }}
                </div>
                <div class="text-xs text-gray-500">{{ schedule.session_name }}</div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="text-center py-8 text-gray-500">
        <i class="fas fa-calendar-times text-4xl mb-2"></i>
        <p>Hôm nay không có lịch dạy</p>
    </div>
    {% endif %}
</div>

<!-- Quick Actions -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- View Schedule -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-calendar-alt text-blue-600 text-xl"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Xem lịch dạy</h3>
                <p class="text-sm text-gray-600">Theo tuần và tháng</p>
            </div>
        </div>
        <div class="space-y-2">
            <a href="{{ url_for('user.weekly_schedule') }}" 
               class="block w-full bg-blue-500 hover:bg-blue-600 text-white text-center py-2 rounded-lg transition-colors duration-200">
                <i class="fas fa-calendar-week mr-2"></i>
                Lịch tuần
            </a>
            <a href="{{ url_for('user.monthly_schedule') }}" 
               class="block w-full bg-blue-100 hover:bg-blue-200 text-blue-700 text-center py-2 rounded-lg transition-colors duration-200">
                <i class="fas fa-calendar mr-2"></i>
                Lịch tháng
            </a>
        </div>
    </div>

    <!-- View Finance -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-chart-line text-green-600 text-xl"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Thu chi</h3>
                <p class="text-sm text-gray-600">Xem báo cáo tài chính</p>
            </div>
        </div>
        <a href="{{ url_for('user.view_finance') }}" 
           class="block w-full bg-green-500 hover:bg-green-600 text-white text-center py-2 rounded-lg transition-colors duration-200">
            <i class="fas fa-eye mr-2"></i>
            Xem thu chi
        </a>
    </div>

    <!-- Attendance Statistics -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-chart-bar text-purple-600 text-xl"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Thống kê</h3>
                <p class="text-sm text-gray-600">Điểm danh học sinh</p>
            </div>
        </div>
        <a href="{{ url_for('user.attendance_statistics') }}" 
           class="block w-full bg-purple-500 hover:bg-purple-600 text-white text-center py-2 rounded-lg transition-colors duration-200">
            <i class="fas fa-chart-bar mr-2"></i>
            Xem thống kê
        </a>
    </div>
</div>
{% endblock %}
