{% extends "base_tailwind.html" %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">
            <i class="fas fa-hands-helping text-green-500 mr-2"></i>
            Quản lý tài sản quyên góp
        </h1>
        <div class="flex flex-wrap gap-3">
            <a href="{{ url_for('financial.dashboard') }}"
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200">
                <i class="fas fa-arrow-left mr-2"></i>Quay lại
            </a>
            <a href="{{ url_for('financial.create_donation') }}"
               class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200">
                <i class="fas fa-plus mr-2"></i><PERSON><PERSON><PERSON><PERSON> quyê<PERSON> gó<PERSON>
            </a>
            <a href="{{ url_for('financial.export_donations', **request.args) }}"
               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200">
                <i class="fas fa-file-excel mr-2"></i>Xuất Excel
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Trạng thái</label>
                <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    <option value="">Tất cả</option>
                    <option value="received" {{ 'selected' if request.args.get('status') == 'received' }}>Đã nhận</option>
                    <option value="distributed" {{ 'selected' if request.args.get('status') == 'distributed' }}>Đã phân phối</option>
                    <option value="damaged" {{ 'selected' if request.args.get('status') == 'damaged' }}>Hư hỏng</option>
                    <option value="lost" {{ 'selected' if request.args.get('status') == 'lost' }}>Mất</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Danh mục</label>
                <select name="category" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    <option value="">Tất cả</option>
                    <option value="equipment" {{ 'selected' if request.args.get('category') == 'equipment' }}>Thiết bị</option>
                    <option value="books" {{ 'selected' if request.args.get('category') == 'books' }}>Sách vở</option>
                    <option value="supplies" {{ 'selected' if request.args.get('category') == 'supplies' }}>Đồ dùng</option>
                    <option value="furniture" {{ 'selected' if request.args.get('category') == 'furniture' }}>Nội thất</option>
                    <option value="electronics" {{ 'selected' if request.args.get('category') == 'electronics' }}>Điện tử</option>
                    <option value="other" {{ 'selected' if request.args.get('category') == 'other' }}>Khác</option>
                </select>
            </div>
            <div class="md:col-span-2 flex items-end">
                <button type="submit" class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-search mr-2"></i>Lọc
                </button>
            </div>
        </form>
    </div>

    <!-- Search -->
    <div class="bg-white rounded-lg shadow-md p-4 mb-6">
        <form method="GET" class="flex gap-4">
            <input type="hidden" name="status" value="{{ request.args.get('status', '') }}">
            <input type="hidden" name="category" value="{{ request.args.get('category', '') }}">
            <div class="flex-1">
                <input type="text" name="search" value="{{ search }}" placeholder="Tìm kiếm theo tên tài sản, người quyên góp..."
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
            </div>
            <button type="submit" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg transition duration-200">
                <i class="fas fa-search"></i>
            </button>
        </form>
    </div>

    <!-- Donations Table -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        {% if donations.items %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tài sản</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Danh mục</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số lượng</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tình trạng</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Người quyên góp</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày quyên góp</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for donation in donations.items %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ donation.asset_name }}</p>
                                    {% if donation.description %}
                                        <p class="text-sm text-gray-500">{{ donation.description[:50] }}{% if donation.description|length > 50 %}...{% endif %}</p>
                                    {% endif %}
                                    {% if donation.estimated_value %}
                                        <p class="text-xs text-green-600">~{{ "{:,.0f}".format(donation.estimated_value) }} VNĐ</p>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ donation.category }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                                {{ donation.quantity }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                           bg-{{ 'green' if donation.condition == 'new' else 'blue' if donation.condition == 'good' else 'yellow' if donation.condition == 'fair' else 'red' }}-100 
                                           text-{{ 'green' if donation.condition == 'new' else 'blue' if donation.condition == 'good' else 'yellow' if donation.condition == 'fair' else 'red' }}-800">
                                    {{ donation.condition_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ donation.donor_name or 'Ẩn danh' }}</p>
                                    {% if donation.donor_phone %}
                                        <p class="text-sm text-gray-500">{{ donation.donor_phone }}</p>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ donation.donation_date.strftime('%d/%m/%Y') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                           bg-{{ donation.status_color }}-100 text-{{ donation.status_color }}-800">
                                    <i class="fas fa-{{ 'check' if donation.status == 'received' else 'share' if donation.status == 'distributed' else 'exclamation-triangle' if donation.status == 'damaged' else 'times' }} mr-1"></i>
                                    {{ donation.status_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewDonation({{ donation.id }})" 
                                            class="text-blue-600 hover:text-blue-900" title="Xem chi tiết">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {% if donation.status == 'received' %}
                                        <a href="{{ url_for('financial.distribute_donation', id=donation.id) }}" 
                                           class="text-green-600 hover:text-green-900" title="Phân phối">
                                            <i class="fas fa-share"></i>
                                        </a>
                                    {% endif %}
                                    <button onclick="updateStatus({{ donation.id }})" 
                                            class="text-orange-600 hover:text-orange-900" title="Cập nhật trạng thái">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button onclick="deleteDonation({{ donation.id }})" 
                                            class="text-red-600 hover:text-red-900" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if donations.pages > 1 %}
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        {% if donations.has_prev %}
                            <a href="{{ url_for('financial.donations', page=donations.prev_num, **request.args) }}" 
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Trước
                            </a>
                        {% endif %}
                        {% if donations.has_next %}
                            <a href="{{ url_for('financial.donations', page=donations.next_num, **request.args) }}" 
                               class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Sau
                            </a>
                        {% endif %}
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Hiển thị <span class="font-medium">{{ donations.per_page * (donations.page - 1) + 1 }}</span>
                                đến <span class="font-medium">{{ donations.per_page * (donations.page - 1) + donations.items|length }}</span>
                                trong <span class="font-medium">{{ donations.total }}</span> kết quả
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                {% for page_num in donations.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != donations.page %}
                                            <a href="{{ url_for('financial.donations', page=page_num, **request.args) }}" 
                                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                                {{ page_num }}
                                            </a>
                                        {% else %}
                                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-green-50 text-sm font-medium text-green-600">
                                                {{ page_num }}
                                            </span>
                                        {% endif %}
                                    {% else %}
                                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                            ...
                                        </span>
                                    {% endif %}
                                {% endfor %}
                            </nav>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-12">
                <i class="fas fa-gift text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Chưa có tài sản quyên góp nào</h3>
                <p class="text-gray-500 mb-6">Bắt đầu bằng cách ghi nhận tài sản quyên góp đầu tiên</p>
                <a href="{{ url_for('financial.create_donation') }}" 
                   class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg transition duration-200">
                    <i class="fas fa-plus mr-2"></i>Nhận quyên góp
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Status Update Modal -->
<div id="statusModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Cập nhật trạng thái</h3>
            <form id="statusForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Trạng thái</label>
                    <select id="statusSelect" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <option value="received">Đã nhận</option>
                        <option value="distributed">Đã phân phối</option>
                        <option value="damaged">Hư hỏng</option>
                        <option value="lost">Mất</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Ghi chú</label>
                    <textarea id="statusNotes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="Ghi chú về việc cập nhật trạng thái"></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeStatusModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        Hủy
                    </button>
                    <button type="submit" class="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg">
                        Cập nhật
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let currentDonationId = null;

function viewDonation(id) {
    // TODO: Implement view donation modal
    alert('Xem chi tiết tài sản quyên góp #' + id);
}

function updateStatus(id) {
    currentDonationId = id;
    document.getElementById('statusModal').classList.remove('hidden');
}

function closeStatusModal() {
    document.getElementById('statusModal').classList.add('hidden');
    currentDonationId = null;
}

function deleteDonation(id) {
    if (confirm('Bạn có chắc chắn muốn xóa tài sản quyên góp này?')) {
        fetch('/financial/donation/' + id + '/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Có lỗi xảy ra: ' + data.message);
            }
        });
    }
}

// Handle status form submission
document.getElementById('statusForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    if (!currentDonationId) return;
    
    const formData = new FormData();
    formData.append('status', document.getElementById('statusSelect').value);
    formData.append('notes', document.getElementById('statusNotes').value);
    
    fetch('/financial/donation/' + currentDonationId + '/update_status', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Có lỗi xảy ra: ' + data.message);
        }
    });
});
</script>
{% endblock %}
