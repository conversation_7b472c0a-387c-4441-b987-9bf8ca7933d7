{% extends "base_tailwind.html" %}

{% block content %}
<!-- Welcome Section -->
<div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg shadow-lg p-6 mb-8 text-white">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold mb-2">
                Ch<PERSON>o mừng, {{ current_user.full_name }}!
            </h1>
            <p class="text-orange-100 text-lg">
                {% if current_user.is_admin() %}
                Quản trị viên hệ thống
                {% elif current_user.is_manager() %}
                Quản sinh - Quản lý lớp học
                {% else %}
                Giáo viên
                {% endif %}
            </p>
        </div>
        <div class="hidden md:block">
            <i class="fas fa-user-circle text-6xl text-orange-200"></i>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Students -->
    <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500 hover:shadow-lg transition-shadow duration-300">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-blue-600 text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Tổng học sinh</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.total_students }}</p>
            </div>
        </div>
    </div>

    <!-- Total Classes -->
    <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500 hover:shadow-lg transition-shadow duration-300">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chalkboard text-green-600 text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Tổng lớp học</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.total_classes }}</p>
            </div>
        </div>
    </div>

    <!-- Total Teachers -->
    <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-purple-500 hover:shadow-lg transition-shadow duration-300">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-user-tie text-purple-600 text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Giáo viên</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.total_teachers }}</p>
            </div>
        </div>
    </div>

    <!-- Today's Schedules -->
    <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-orange-500 hover:shadow-lg transition-shadow duration-300">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-calendar-check text-orange-600 text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Lịch hôm nay</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.today_schedules }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Teacher-specific stats -->
{% if current_user.is_teacher() %}
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-indigo-500">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chalkboard-teacher text-indigo-600 text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Lớp đang dạy</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.teaching_classes }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-pink-500">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-pink-600 text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Buổi/tuần</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.weekly_sessions }}</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Today's Schedule -->
    <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-md">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-calendar-day text-orange-500 mr-2"></i>
                    Lịch dạy hôm nay
                </h3>
            </div>
            <div class="p-6">
                {% if today_schedules %}
                <div class="space-y-4">
                    {% for schedule in today_schedules %}
                    <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors duration-200">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <h4 class="font-semibold text-gray-900 flex items-center">
                                    <i class="fas fa-chalkboard text-orange-500 mr-2"></i>
                                    {{ schedule.class_obj.name }}
                                </h4>
                                <p class="text-sm text-gray-600 mt-1">
                                    <i class="fas fa-clock mr-1"></i>
                                    <strong>{{ schedule.session_name }}:</strong> {{ schedule.time_range }}
                                </p>
                                {% if schedule.subject %}
                                <p class="text-sm text-gray-600">
                                    <i class="fas fa-book mr-1"></i>
                                    <strong>Môn:</strong> {{ schedule.subject }}
                                </p>
                                {% endif %}
                                {% if schedule.room %}
                                <p class="text-sm text-gray-600">
                                    <i class="fas fa-door-open mr-1"></i>
                                    <strong>Phòng:</strong> {{ schedule.room }}
                                </p>
                                {% endif %}
                                <p class="text-sm text-gray-600">
                                    <i class="fas fa-users mr-1"></i>
                                    <strong>Sĩ số:</strong> {{ schedule.class_obj.student_count }} HS
                                </p>
                            </div>
                            {% if current_user.is_teacher() and schedule.teacher_id == current_user.id %}
                            <div class="ml-4">
                                <a href="{{ url_for('teacher.attendance', schedule_id=schedule.id) }}" 
                                   class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200">
                                    <i class="fas fa-user-check mr-1"></i>
                                    Điểm danh
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-8">
                    <i class="fas fa-calendar-times text-gray-400 text-4xl mb-4"></i>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">Không có lịch dạy hôm nay</h4>
                    <p class="text-gray-600">Hãy nghỉ ngơi và chuẩn bị cho những ngày tiếp theo!</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Quick Actions & Recent Activity -->
    <div class="space-y-6">
        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-bolt text-orange-500 mr-2"></i>
                    Thao tác nhanh
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{{ url_for('calendar.calendar_view') }}" 
                       class="flex items-center p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors duration-200">
                        <i class="fas fa-calendar text-orange-600 mr-3"></i>
                        <span class="text-gray-900 font-medium">Xem lịch dạy</span>
                    </a>
                    
                    {% if current_user.is_admin() or current_user.is_manager() %}
                    <a href="{{ url_for('manager.classes') }}" 
                       class="flex items-center p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200">
                        <i class="fas fa-chalkboard text-blue-600 mr-3"></i>
                        <span class="text-gray-900 font-medium">Quản lý lớp học</span>
                    </a>
                    
                    <a href="{{ url_for('manager.students') }}" 
                       class="flex items-center p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors duration-200">
                        <i class="fas fa-user-graduate text-green-600 mr-3"></i>
                        <span class="text-gray-900 font-medium">Quản lý học sinh</span>
                    </a>
                    {% endif %}
                    
                    {% if current_user.is_admin() %}
                    <a href="{{ url_for('admin.users') }}" 
                       class="flex items-center p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors duration-200">
                        <i class="fas fa-users text-purple-600 mr-3"></i>
                        <span class="text-gray-900 font-medium">Quản lý người dùng</span>
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- System Info -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-info-circle text-orange-500 mr-2"></i>
                    Thông tin hệ thống
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Phiên bản:</span>
                        <span class="font-medium text-gray-900">v1.0.0</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Ngày cập nhật:</span>
                        <span class="font-medium text-gray-900">23/06/2024</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Trạng thái:</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-circle text-green-400 mr-1 text-xs"></i>
                            Hoạt động
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Add some interactive effects
document.addEventListener('DOMContentLoaded', function() {
    // Animate statistics cards on load
    const statCards = document.querySelectorAll('.grid > div');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Add hover effects to schedule items
    const scheduleItems = document.querySelectorAll('.border.border-gray-200');
    scheduleItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
});
</script>
{% endblock %}
