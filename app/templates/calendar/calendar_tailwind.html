{% extends "base_tailwind.html" %}

{% block content %}
<!-- Header -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-calendar text-orange-500 mr-3"></i>
                Lịch dạy tuần
            </h1>
            <p class="text-gray-600 mt-1">Tuần {{ current_week }}, {{ current_year }}</p>
        </div>
        
        <!-- Navigation Controls -->
        <div class="flex items-center space-x-2">
            <a href="{{ url_for('calendar.calendar_view', year=prev_year, week=prev_week) }}" 
               class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-md transition-colors duration-200">
                <i class="fas fa-chevron-left"></i>
            </a>
            
            <span class="px-4 py-2 bg-orange-100 text-orange-800 rounded-md font-medium">
                {{ week_start.strftime('%d/%m') }} - {{ week_end.strftime('%d/%m/%Y') }}
            </span>
            
            <a href="{{ url_for('calendar.calendar_view', year=next_year, week=next_week) }}" 
               class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-md transition-colors duration-200">
                <i class="fas fa-chevron-right"></i>
            </a>
            
            <div class="hidden sm:flex items-center space-x-2 ml-4">
                <a href="{{ url_for('calendar.calendar_view') }}"
                   class="bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-md transition-colors duration-200">
                    <i class="fas fa-calendar-day mr-1"></i>
                    Hôm nay
                </a>
                <a href="{{ url_for('calendar.month_view') }}"
                   class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors duration-200">
                    <i class="fas fa-calendar-alt mr-1"></i>
                    Tháng
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Calendar Grid -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <!-- Calendar Header -->
    <div class="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
        <div class="grid grid-cols-8 gap-0">
            <div class="p-4 text-center font-semibold border-r border-orange-400">
                <i class="fas fa-clock"></i>
            </div>
            {% set day_names = ['Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật'] %}
            {% for day_name in day_names %}
            <div class="p-4 text-center font-semibold {% if not loop.last %}border-r border-orange-400{% endif %}">
                <div class="text-sm">{{ day_name }}</div>
                <div class="text-xs opacity-90 mt-1">
                    {{ (week_start + timedelta(days=loop.index0)).strftime('%d/%m') }}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Calendar Body -->
    <div class="divide-y divide-gray-200">
        <!-- Morning Session -->
        <div class="grid grid-cols-8 gap-0 min-h-32">
            <div class="bg-blue-50 border-r border-gray-200 p-4 flex flex-col justify-center items-center">
                <div class="text-sm font-semibold text-blue-700">Buổi sáng</div>
                <div class="text-xs text-blue-600 mt-1">07:30 - 11:30</div>
            </div>
            
            {% for day in range(1, 8) %}
            <div class="border-r border-gray-200 p-2 min-h-32 {% if not loop.last %}{% endif %}">
                {% if 'morning' in calendar_data[day].periods %}
                    {% for item in calendar_data[day].periods['morning'] %}
                    <div class="schedule-item bg-gradient-to-r from-blue-500 to-blue-600 text-white p-3 rounded-lg mb-2 cursor-pointer hover:shadow-lg transition-all duration-200 transform hover:scale-105"
                         onclick="viewScheduleDetail({{ item.schedule.id }}, '{{ item.date.strftime('%Y-%m-%d') }}')">
                        <div class="font-semibold text-sm mb-1">{{ item.schedule.class_obj.name }}</div>
                        <div class="text-xs opacity-90">{{ item.schedule.teacher.full_name }}</div>
                        <div class="text-xs opacity-90">{{ item.schedule.time_range }}</div>
                        {% if item.schedule.subject %}
                        <div class="text-xs opacity-90">{{ item.schedule.subject }}</div>
                        {% endif %}
                        {% if item.has_attendance %}
                        <div class="text-xs mt-1">
                            <i class="fas fa-check-circle"></i> Đã điểm danh
                        </div>
                        {% else %}
                        <div class="text-xs mt-1 opacity-75">
                            <i class="fas fa-clock"></i> Chưa điểm danh
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <!-- Afternoon Session -->
        <div class="grid grid-cols-8 gap-0 min-h-32">
            <div class="bg-green-50 border-r border-gray-200 p-4 flex flex-col justify-center items-center">
                <div class="text-sm font-semibold text-green-700">Buổi chiều</div>
                <div class="text-xs text-green-600 mt-1">13:30 - 17:30</div>
            </div>
            
            {% for day in range(1, 8) %}
            <div class="border-r border-gray-200 p-2 min-h-32 {% if not loop.last %}{% endif %}">
                {% if 'afternoon' in calendar_data[day].periods %}
                    {% for item in calendar_data[day].periods['afternoon'] %}
                    <div class="schedule-item bg-gradient-to-r from-green-500 to-green-600 text-white p-3 rounded-lg mb-2 cursor-pointer hover:shadow-lg transition-all duration-200 transform hover:scale-105"
                         onclick="viewScheduleDetail({{ item.schedule.id }}, '{{ item.date.strftime('%Y-%m-%d') }}')">
                        <div class="font-semibold text-sm mb-1">{{ item.schedule.class_obj.name }}</div>
                        <div class="text-xs opacity-90">{{ item.schedule.teacher.full_name }}</div>
                        <div class="text-xs opacity-90">{{ item.schedule.time_range }}</div>
                        {% if item.schedule.subject %}
                        <div class="text-xs opacity-90">{{ item.schedule.subject }}</div>
                        {% endif %}
                        {% if item.has_attendance %}
                        <div class="text-xs mt-1">
                            <i class="fas fa-check-circle"></i> Đã điểm danh
                        </div>
                        {% else %}
                        <div class="text-xs mt-1 opacity-75">
                            <i class="fas fa-clock"></i> Chưa điểm danh
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <!-- Evening Session -->
        <div class="grid grid-cols-8 gap-0 min-h-32">
            <div class="bg-purple-50 border-r border-gray-200 p-4 flex flex-col justify-center items-center">
                <div class="text-sm font-semibold text-purple-700">Buổi tối</div>
                <div class="text-xs text-purple-600 mt-1">18:30 - 21:30</div>
            </div>

            {% for day in range(1, 8) %}
            <div class="border-r border-gray-200 p-2 min-h-32 {% if not loop.last %}{% endif %}">
                {% if 'evening' in calendar_data[day].periods %}
                    {% for item in calendar_data[day].periods['evening'] %}
                    <div class="schedule-item bg-gradient-to-r from-purple-500 to-purple-600 text-white p-3 rounded-lg mb-2 cursor-pointer hover:shadow-lg transition-all duration-200 transform hover:scale-105"
                         onclick="viewScheduleDetail({{ item.schedule.id }}, '{{ item.date.strftime('%Y-%m-%d') }}')">
                        <div class="font-semibold text-sm mb-1">{{ item.schedule.class_obj.name }}</div>
                        <div class="text-xs opacity-90">{{ item.schedule.teacher.full_name }}</div>
                        <div class="text-xs opacity-90">{{ item.schedule.time_range }}</div>
                        {% if item.schedule.subject %}
                        <div class="text-xs opacity-90">{{ item.schedule.subject }}</div>
                        {% endif %}
                        {% if item.has_attendance %}
                        <div class="text-xs mt-1">
                            <i class="fas fa-check-circle"></i> Đã điểm danh
                        </div>
                        {% else %}
                        <div class="text-xs mt-1 opacity-75">
                            <i class="fas fa-clock"></i> Chưa điểm danh
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Legend -->
<div class="mt-6 bg-white rounded-lg shadow-md p-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Chú thích</h3>
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="flex items-center">
            <div class="w-4 h-4 bg-gradient-to-r from-blue-500 to-blue-600 rounded mr-2"></div>
            <span class="text-sm text-gray-700">Buổi sáng</span>
        </div>
        <div class="flex items-center">
            <div class="w-4 h-4 bg-gradient-to-r from-green-500 to-green-600 rounded mr-2"></div>
            <span class="text-sm text-gray-700">Buổi chiều</span>
        </div>
        <div class="flex items-center">
            <div class="w-4 h-4 bg-gradient-to-r from-purple-500 to-purple-600 rounded mr-2"></div>
            <span class="text-sm text-gray-700">Buổi tối</span>
        </div>
        <div class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-2"></i>
            <span class="text-sm text-gray-700">Đã điểm danh</span>
        </div>
        <div class="flex items-center">
            <i class="fas fa-clock text-yellow-500 mr-2"></i>
            <span class="text-sm text-gray-700">Chưa điểm danh</span>
        </div>
    </div>
</div>

<!-- Schedule Detail Modal -->
<div id="scheduleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-96 overflow-y-auto">
            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Chi tiết lịch học</h3>
                <button onclick="closeScheduleModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="scheduleModalContent" class="p-6">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function viewScheduleDetail(scheduleId, date) {
    // Show modal
    document.getElementById('scheduleModal').classList.remove('hidden');
    
    // Load content
    fetch(`/calendar/schedule/${scheduleId}/detail?date=${date}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('scheduleModalContent').innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading schedule details:', error);
            notify.error('Không thể tải chi tiết lịch học');
            closeScheduleModal();
        });
}

function closeScheduleModal() {
    document.getElementById('scheduleModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('scheduleModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeScheduleModal();
    }
});

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeScheduleModal();
    }
});

// Add animation to schedule items
document.addEventListener('DOMContentLoaded', function() {
    const scheduleItems = document.querySelectorAll('.schedule-item');
    scheduleItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        setTimeout(() => {
            item.style.transition = 'all 0.3s ease-out';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 50);
    });
});

// Add hover effects
document.querySelectorAll('.schedule-item').forEach(item => {
    item.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.05)';
        this.style.zIndex = '10';
    });
    
    item.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1)';
        this.style.zIndex = 'auto';
    });
});

// Show schedule details in modal
function viewScheduleDetails(scheduleId, date) {
    fetch(`/calendar/schedule/${scheduleId}/detail?date=${date}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('scheduleModalContent').innerHTML = html;
            document.getElementById('scheduleModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi tải thông tin lịch học');
        });
}

// Delete schedule from calendar
function deleteScheduleFromCalendar(scheduleId) {
    if (!confirm('Bạn có chắc chắn muốn xóa lịch dạy này?\n\nViệc xóa sẽ:\n- Hủy đăng ký tất cả học sinh\n- Xóa tất cả dữ liệu điểm danh\n- Không thể hoàn tác')) {
        return;
    }

    // Get CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';

    fetch(`/manager/schedule/${scheduleId}/delete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            // Close modal
            document.getElementById('scheduleModal').classList.add('hidden');
            // Reload calendar
            location.reload();
        } else {
            alert('Lỗi: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi xóa lịch dạy');
    });
}
</script>
{% endblock %}
