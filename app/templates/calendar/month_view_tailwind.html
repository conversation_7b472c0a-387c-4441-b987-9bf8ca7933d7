{% extends "base_tailwind.html" %}

{% block content %}
<!-- Header -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-calendar text-orange-500 mr-3"></i>
                Lịch tháng {{ current_month }}/{{ current_year }}
            </h1>
            <p class="text-gray-600 mt-1">Xem tổng quan lịch dạy trong tháng</p>
        </div>
        
        <div class="flex items-center space-x-2">
            <a href="{{ url_for('calendar.month_view', year=prev_month.year, month=prev_month.month) }}"
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-chevron-left mr-2"></i>
                Tháng trước
            </a>

            <a href="{{ url_for('calendar.calendar_view') }}"
               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-calendar-week mr-2"></i>
                Lịch tuần
            </a>

            <a href="{{ url_for('calendar.month_view', year=next_month.year, month=next_month.month) }}"
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                Tháng sau
                <i class="fas fa-chevron-right ml-2"></i>
            </a>
        </div>
    </div>
</div>

<!-- Month Info -->
<div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg shadow-md p-6 mb-6 text-white">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
        <div>
            <div class="text-2xl font-bold">{{ month_name }} {{ current_year }}</div>
            <div class="text-orange-200">Tháng hiện tại</div>
        </div>
        <div>
            <div class="text-2xl font-bold">{{ total_schedules }}</div>
            <div class="text-orange-200">Tổng tiết học</div>
        </div>
        <div>
            <div class="text-2xl font-bold">{{ working_days }}</div>
            <div class="text-orange-200">Ngày có lịch</div>
        </div>
        <div>
            <div class="text-2xl font-bold">{{ total_classes }}</div>
            <div class="text-orange-200">Lớp học</div>
        </div>
    </div>
</div>

<!-- Calendar Grid -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <h3 class="text-lg font-semibold text-gray-900">{{ month_name }} {{ current_year }}</h3>
    </div>
    
    <div class="p-6">
        <!-- Days of Week Header -->
        <div class="grid grid-cols-7 gap-1 mb-2">
            {% for day in ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'] %}
            <div class="text-center py-2 text-sm font-medium text-gray-600 bg-gray-100 rounded">
                {{ day }}
            </div>
            {% endfor %}
        </div>
        
        <!-- Calendar Days -->
        <div class="grid grid-cols-7 gap-1">
            {% for week in calendar_weeks %}
                {% for day in week %}
                <div class="min-h-24 border border-gray-200 rounded-lg p-2 
                    {% if day.is_current_month %}bg-white hover:bg-gray-50
                    {% else %}bg-gray-100{% endif %}
                    {% if day.is_today %}ring-2 ring-orange-500{% endif %}
                    transition-colors duration-200">
                    
                    <!-- Date Number -->
                    <div class="flex justify-between items-start mb-1">
                        <span class="text-sm font-medium 
                            {% if day.is_current_month %}text-gray-900
                            {% else %}text-gray-400{% endif %}
                            {% if day.is_today %}text-orange-600 font-bold{% endif %}">
                            {{ day.date.day }}
                        </span>
                        
                        {% if day.schedules %}
                        <span class="inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-white bg-orange-500 rounded-full">
                            {{ day.schedules|length }}
                        </span>
                        {% endif %}
                    </div>
                    
                    <!-- Schedules -->
                    {% if day.schedules and day.is_current_month %}
                    <div class="space-y-1">
                        {% for schedule in day.schedules[:3] %}
                        <div class="text-xs p-1 rounded cursor-pointer
                            {% if schedule.session == 'morning' %}bg-blue-100 text-blue-800 hover:bg-blue-200
                            {% else %}bg-green-100 text-green-800 hover:bg-green-200{% endif %}
                            transition-colors duration-200"
                             onclick="viewScheduleDetails({{ schedule.id }}, '{{ day.date.strftime('%Y-%m-%d') }}')"
                             title="{{ schedule.class_obj.name }} - {{ schedule.teacher.full_name }}">
                            <div class="truncate">{{ schedule.class_obj.name }}</div>
                            <div class="truncate text-xs opacity-75">{{ schedule.time_range }}</div>
                        </div>
                        {% endfor %}
                        
                        {% if day.schedules|length > 3 %}
                        <div class="text-xs text-gray-500 text-center">
                            +{{ day.schedules|length - 3 }} khác
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            {% endfor %}
        </div>
    </div>
</div>

<!-- Legend -->
<div class="bg-white rounded-lg shadow-md p-6 mt-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <i class="fas fa-info-circle text-blue-500 mr-2"></i>
        Chú thích
    </h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="flex items-center">
            <div class="w-4 h-4 bg-blue-100 border border-blue-300 rounded mr-2"></div>
            <span class="text-sm text-gray-700">Buổi sáng</span>
        </div>
        <div class="flex items-center">
            <div class="w-4 h-4 bg-green-100 border border-green-300 rounded mr-2"></div>
            <span class="text-sm text-gray-700">Buổi chiều</span>
        </div>
        <div class="flex items-center">
            <div class="w-4 h-4 border-2 border-orange-500 rounded mr-2"></div>
            <span class="text-sm text-gray-700">Hôm nay</span>
        </div>
    </div>
</div>

<!-- Schedule Details Modal -->
<div id="scheduleDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-96 overflow-y-auto">
            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Chi tiết lịch học</h3>
                <button onclick="closeScheduleDetailsModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="scheduleDetailsContent" class="p-6">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function viewScheduleDetails(scheduleId, date) {
    document.getElementById('scheduleDetailsModal').classList.remove('hidden');
    document.getElementById('scheduleDetailsContent').innerHTML = '<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-orange-500 text-2xl"></i><p class="mt-2 text-gray-600">Đang tải...</p></div>';
    
    // Load schedule details via AJAX
    fetch(`/calendar/schedule/${scheduleId}/detail?date=${date}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('scheduleDetailsContent').innerHTML = html;
        })
        .catch(error => {
            document.getElementById('scheduleDetailsContent').innerHTML = '<div class="text-center py-8 text-red-500"><i class="fas fa-exclamation-triangle text-2xl"></i><p class="mt-2">Lỗi khi tải thông tin</p></div>';
        });
}

function closeScheduleDetailsModal() {
    document.getElementById('scheduleDetailsModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('scheduleDetailsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeScheduleDetailsModal();
    }
});

// Animation on load
document.addEventListener('DOMContentLoaded', function() {
    const days = document.querySelectorAll('.grid.grid-cols-7 > div');
    days.forEach((day, index) => {
        if (index >= 7) { // Skip header row
            day.style.opacity = '0';
            day.style.transform = 'scale(0.9)';
            setTimeout(() => {
                day.style.transition = 'all 0.3s ease-out';
                day.style.opacity = '1';
                day.style.transform = 'scale(1)';
            }, (index - 7) * 20);
        }
    });
});

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'ArrowLeft') {
        document.querySelector('a[href*="prev_month"]')?.click();
    } else if (e.key === 'ArrowRight') {
        document.querySelector('a[href*="next_month"]')?.click();
    }
});
</script>
{% endblock %}
