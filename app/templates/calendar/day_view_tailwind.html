{% extends "base_tailwind.html" %}

{% block content %}
<!-- Header -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-calendar-day text-orange-500 mr-3"></i>
                Lịch ngày {{ view_date.strftime('%d/%m/%Y') }}
            </h1>
            <p class="text-gray-600 mt-1">{{ day_name }} - Xem chi tiết lịch dạy trong ngày</p>
        </div>
        
        <div class="flex items-center space-x-2">
            <a href="{{ url_for('calendar.day_view', date_str=(view_date - timedelta(days=1)).strftime('%Y-%m-%d')) }}" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-chevron-left mr-2"></i>
                Ngày trước
            </a>
            
            <a href="{{ url_for('calendar.calendar_view') }}" 
               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-calendar mr-2"></i>
                Lịch tuần
            </a>
            
            <a href="{{ url_for('calendar.day_view', date_str=(view_date + timedelta(days=1)).strftime('%Y-%m-%d')) }}" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                Ngày sau
                <i class="fas fa-chevron-right ml-2"></i>
            </a>
        </div>
    </div>
</div>

<!-- Date Info Card -->
<div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg shadow-md p-6 mb-6 text-white">
    <div class="flex items-center justify-between">
        <div>
            <h2 class="text-2xl font-bold mb-2">{{ day_name }}</h2>
            <p class="text-orange-100 text-lg">{{ view_date.strftime('%d tháng %m, %Y') }}</p>
        </div>
        <div class="text-right">
            <div class="text-3xl font-bold">{{ schedule_data|length }}</div>
            <div class="text-orange-200">Tiết học</div>
        </div>
    </div>
</div>

<!-- Schedule List -->
{% if schedule_data %}
<div class="space-y-4">
    {% for item in schedule_data %}
    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200">
        <div class="p-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <!-- Schedule Info -->
                <div class="flex-1">
                    <div class="flex items-center mb-3">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-chalkboard text-orange-600 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">{{ item.schedule.class_obj.name }}</h3>
                            <p class="text-gray-600">{{ item.schedule.subject or 'Chưa xác định môn học' }}</p>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div class="flex items-center text-gray-600">
                            <i class="fas fa-clock text-blue-500 mr-2"></i>
                            <span>{{ item.schedule.time_range }}</span>
                        </div>
                        <div class="flex items-center text-gray-600">
                            <i class="fas fa-user-tie text-green-500 mr-2"></i>
                            <span>{{ item.schedule.teacher.full_name }}</span>
                        </div>
                        <div class="flex items-center text-gray-600">
                            <i class="fas fa-door-open text-purple-500 mr-2"></i>
                            <span>{{ item.schedule.room or 'Chưa xác định phòng' }}</span>
                        </div>
                    </div>
                </div>

                <!-- Session Badge -->
                <div class="flex items-center space-x-3">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                        {% if item.schedule.session == 'morning' %}bg-blue-100 text-blue-800
                        {% else %}bg-green-100 text-green-800{% endif %}">
                        {% if item.schedule.session == 'morning' %}
                            <i class="fas fa-sun mr-1"></i> Buổi sáng
                        {% else %}
                            <i class="fas fa-moon mr-1"></i> Buổi chiều
                        {% endif %}
                    </span>
                </div>
            </div>

            <!-- Attendance Summary -->
            {% if item.attendance_summary %}
            <div class="mt-4 pt-4 border-t border-gray-200">
                <h4 class="text-sm font-medium text-gray-900 mb-3 flex items-center">
                    <i class="fas fa-user-check text-orange-500 mr-2"></i>
                    Tình hình điểm danh
                </h4>
                <div class="grid grid-cols-3 gap-4">
                    <div class="text-center p-3 bg-green-50 rounded-lg">
                        <div class="text-lg font-bold text-green-600">{{ item.attendance_summary.present }}</div>
                        <div class="text-xs text-green-700">Có mặt</div>
                    </div>
                    <div class="text-center p-3 bg-red-50 rounded-lg">
                        <div class="text-lg font-bold text-red-600">{{ item.attendance_summary.absent_with_reason + item.attendance_summary.absent_without_reason }}</div>
                        <div class="text-xs text-red-700">Vắng mặt</div>
                    </div>
                    <div class="text-center p-3 bg-yellow-50 rounded-lg">
                        <div class="text-lg font-bold text-yellow-600">{{ item.attendance_summary.not_taken }}</div>
                        <div class="text-xs text-yellow-700">Chưa điểm danh</div>
                    </div>
                </div>
                <div class="mt-2 text-center">
                    <span class="text-sm text-gray-600">
                        Tổng: {{ item.attendance_summary.total }} học sinh
                    </span>
                </div>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="mt-4 pt-4 border-t border-gray-200 flex flex-wrap gap-2">
                <button onclick="viewScheduleDetails({{ item.schedule.id }}, '{{ view_date.strftime('%Y-%m-%d') }}')"
                        class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 text-sm">
                    <i class="fas fa-eye mr-2"></i>
                    Chi tiết
                </button>

                {% if item.can_take_attendance %}
                <a href="{{ url_for('teacher.attendance', schedule_id=item.schedule.id, date=view_date.strftime('%Y-%m-%d')) }}"
                   class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 text-sm">
                    <i class="fas fa-user-check mr-2"></i>
                    Điểm danh
                </a>
                {% endif %}
                
                {% if current_user.is_admin() or current_user.is_manager() %}
                <a href="{{ url_for('manager.schedule') }}" 
                   class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 text-sm">
                    <i class="fas fa-edit mr-2"></i>
                    Quản lý
                </a>
                {% endif %}
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<!-- Empty State -->
<div class="bg-white rounded-lg shadow-md p-12 text-center">
    <i class="fas fa-calendar-times text-gray-400 text-6xl mb-4"></i>
    <h3 class="text-xl font-semibold text-gray-900 mb-2">Không có lịch dạy</h3>
    <p class="text-gray-600 mb-6">Không có tiết học nào được lên lịch cho ngày {{ day_name }}, {{ view_date.strftime('%d/%m/%Y') }}</p>
    
    <div class="flex flex-wrap justify-center gap-3">
        <a href="{{ url_for('calendar.calendar_view') }}" 
           class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors duration-200">
            <i class="fas fa-calendar mr-2"></i>
            Xem lịch tuần
        </a>
        
        {% if current_user.is_admin() or current_user.is_manager() %}
        <a href="{{ url_for('manager.schedule') }}" 
           class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-colors duration-200">
            <i class="fas fa-plus mr-2"></i>
            Thêm lịch dạy
        </a>
        {% endif %}
    </div>
</div>
{% endif %}

<!-- Schedule Details Modal -->
<div id="scheduleDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-96 overflow-y-auto">
            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Chi tiết lịch học</h3>
                <button onclick="closeScheduleDetailsModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="scheduleDetailsContent" class="p-6">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function viewScheduleDetails(scheduleId, date) {
    document.getElementById('scheduleDetailsModal').classList.remove('hidden');
    document.getElementById('scheduleDetailsContent').innerHTML = '<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-orange-500 text-2xl"></i><p class="mt-2 text-gray-600">Đang tải...</p></div>';
    
    // Load schedule details via AJAX
    fetch(`/calendar/schedule/${scheduleId}/detail?date=${date}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('scheduleDetailsContent').innerHTML = html;
        })
        .catch(error => {
            document.getElementById('scheduleDetailsContent').innerHTML = '<div class="text-center py-8 text-red-500"><i class="fas fa-exclamation-triangle text-2xl"></i><p class="mt-2">Lỗi khi tải thông tin</p></div>';
        });
}

function closeScheduleDetailsModal() {
    document.getElementById('scheduleDetailsModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('scheduleDetailsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeScheduleDetailsModal();
    }
});

// Animation on load
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.bg-white');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
