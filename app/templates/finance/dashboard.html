{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="fas fa-chart-line"></i> <PERSON>u<PERSON>n lý tà<PERSON> ch<PERSON>h</h2>
            <div class="btn-group">
                <a href="{{ url_for('finance.add_transaction') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Thêm giao dịch
                </a>
                <a href="{{ url_for('finance.transactions') }}" class="btn btn-outline-primary">
                    <i class="fas fa-list"></i> Danh sách giao dịch
                </a>
                <a href="{{ url_for('finance.financial_report') }}" class="btn btn-outline-info">
                    <i class="fas fa-chart-bar"></i> Báo cáo
                </a>
            </div>
        </div>
        <hr>
    </div>
</div>

<!-- Date Range Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">Từ ngày</label>
                        <input type="date" name="start_date" class="form-control" 
                               value="{{ start_date.strftime('%Y-%m-%d') }}">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Đến ngày</label>
                        <input type="date" name="end_date" class="form-control" 
                               value="{{ end_date.strftime('%Y-%m-%d') }}">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter"></i> Lọc
                            </button>
                            <a href="{{ url_for('finance.finance_dashboard') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-refresh"></i> Đặt lại
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ "{:,.0f}".format(total_income) }} VNĐ</h4>
                        <p class="mb-0">Tổng thu</p>
                        <small>{{ income_count }} giao dịch</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-arrow-up fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ "{:,.0f}".format(total_expense) }} VNĐ</h4>
                        <p class="mb-0">Tổng chi</p>
                        <small>{{ expense_count }} giao dịch</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-arrow-down fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card {% if balance >= 0 %}bg-info{% else %}bg-warning{% endif %} text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ "{:,.0f}".format(balance) }} VNĐ</h4>
                        <p class="mb-0">Số dư</p>
                        <small>{% if balance >= 0 %}Dương{% else %}Âm{% endif %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-balance-scale fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ income_count + expense_count }}</h4>
                        <p class="mb-0">Tổng giao dịch</p>
                        <small>Trong kỳ</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exchange-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line"></i> Biểu đồ thu chi theo tháng</h5>
            </div>
            <div class="card-body">
                <canvas id="monthlyChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> Tỷ lệ thu/chi</h5>
            </div>
            <div class="card-body">
                <canvas id="pieChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Transactions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock"></i> Giao dịch gần đây</h5>
            </div>
            <div class="card-body">
                {% if recent_transactions %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Ngày</th>
                                <th>Loại</th>
                                <th>Mô tả</th>
                                <th>Danh mục</th>
                                <th>Số tiền</th>
                                <th>Người tạo</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in recent_transactions %}
                            <tr>
                                <td>{{ transaction.transaction_date.strftime('%d/%m/%Y') }}</td>
                                <td>
                                    {% if transaction.type == 'income' %}
                                        <span class="badge bg-success">Thu</span>
                                    {% else %}
                                        <span class="badge bg-danger">Chi</span>
                                    {% endif %}
                                </td>
                                <td>{{ transaction.description }}</td>
                                <td>{{ transaction.category or '-' }}</td>
                                <td class="{% if transaction.type == 'income' %}text-success{% else %}text-danger{% endif %}">
                                    {% if transaction.type == 'income' %}+{% else %}-{% endif %}{{ "{:,.0f}".format(transaction.amount) }} VNĐ
                                </td>
                                <td>{{ transaction.creator.full_name }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('finance.transactions') }}" class="btn btn-outline-primary">
                        Xem tất cả giao dịch
                    </a>
                </div>
                {% else %}
                <div class="text-center text-muted">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>Chưa có giao dịch nào trong kỳ này</p>
                    <a href="{{ url_for('finance.add_transaction') }}" class="btn btn-primary">
                        Thêm giao dịch đầu tiên
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Monthly Chart
const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
const monthlyData = {{ monthly_data | tojson }};

const months = Object.keys(monthlyData).sort();
const incomeData = months.map(month => monthlyData[month].income || 0);
const expenseData = months.map(month => monthlyData[month].expense || 0);

new Chart(monthlyCtx, {
    type: 'line',
    data: {
        labels: months.map(m => {
            const [year, month] = m.split('-');
            return `${month}/${year}`;
        }),
        datasets: [{
            label: 'Thu',
            data: incomeData,
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.4
        }, {
            label: 'Chi',
            data: expenseData,
            borderColor: '#ef4444',
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return new Intl.NumberFormat('vi-VN').format(value) + ' VNĐ';
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': ' + 
                               new Intl.NumberFormat('vi-VN').format(context.parsed.y) + ' VNĐ';
                    }
                }
            }
        }
    }
});

// Pie Chart
const pieCtx = document.getElementById('pieChart').getContext('2d');
new Chart(pieCtx, {
    type: 'doughnut',
    data: {
        labels: ['Thu', 'Chi'],
        datasets: [{
            data: [{{ total_income }}, {{ total_expense }}],
            backgroundColor: ['#10b981', '#ef4444'],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const total = {{ total_income + total_expense }};
                        const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
                        return context.label + ': ' + 
                               new Intl.NumberFormat('vi-VN').format(context.parsed) + ' VNĐ (' + percentage + '%)';
                    }
                }
            }
        }
    }
});
</script>
{% endblock %}
