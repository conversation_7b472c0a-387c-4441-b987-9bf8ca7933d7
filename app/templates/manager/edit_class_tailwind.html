{% extends "base_tailwind.html" %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ title }}</h1>
                <p class="text-gray-600 mt-1">Chỉnh sửa thông tin lớp học</p>
            </div>
            <a href="{{ url_for('manager.classes') }}" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-arrow-left mr-2"></i>
                Quay lại
            </a>
        </div>
    </div>

    <!-- Current Class Info -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div class="flex items-start">
            <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-2"></i>
            <div>
                <h4 class="text-sm font-medium text-blue-800">Thông tin hiện tại</h4>
                <p class="text-sm text-blue-700 mt-1">
                    <strong>Lớp:</strong> {{ class_obj.name }} | 
                    <strong>Quản sinh:</strong> {{ class_obj.manager.full_name if class_obj.manager else 'Chưa phân công' }} |
                    <strong>Trạng thái:</strong> {{ 'Hoạt động' if class_obj.is_active else 'Không hoạt động' }}
                </p>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <form method="POST" class="space-y-6">
            {{ form.hidden_tag() }}

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Class Name -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-chalkboard text-orange-500 mr-1"></i>
                        Tên lớp *
                    </label>
                    {{ form.name(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500") }}
                    {% if form.name.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.name.errors[0] }}</p>
                    {% endif %}
                </div>

                <!-- Manager -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user-tie text-blue-500 mr-1"></i>
                        Quản sinh
                    </label>
                    {{ form.manager_id(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500") }}
                    {% if form.manager_id.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.manager_id.errors[0] }}</p>
                    {% endif %}
                </div>

                <!-- Description -->
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-align-left text-green-500 mr-1"></i>
                        Mô tả
                    </label>
                    {{ form.description(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500", rows="3", placeholder="Nhập mô tả về lớp học") }}
                    {% if form.description.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.description.errors[0] }}</p>
                    {% endif %}
                </div>

                <!-- Status -->
                <div class="md:col-span-2">
                    <label class="flex items-center">
                        {{ form.is_active(class="w-4 h-4 text-orange-600 bg-gray-100 border-gray-300 rounded focus:ring-orange-500") }}
                        <span class="ml-2 text-sm font-medium text-gray-700">
                            <i class="fas fa-toggle-on text-green-500 mr-1"></i>
                            Lớp học đang hoạt động
                        </span>
                    </label>
                    {% if form.is_active.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.is_active.errors[0] }}</p>
                    {% endif %}
                </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <div class="text-sm text-gray-600">
                    <i class="fas fa-info-circle mr-1"></i>
                    Các trường có dấu (*) là bắt buộc
                </div>
                <div class="flex space-x-3">
                    <a href="{{ url_for('manager.classes') }}" 
                       class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-times mr-2"></i>
                        Hủy
                    </a>
                    <button type="submit" 
                            class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i>
                        Cập nhật
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Class Statistics -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Thống kê lớp học</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-blue-50 rounded-lg p-4">
                <div class="flex items-center">
                    <i class="fas fa-users text-blue-600 text-xl mr-3"></i>
                    <div>
                        <p class="text-sm text-blue-600">Tổng học sinh</p>
                        <p class="text-xl font-bold text-blue-800">{{ class_obj.students|selectattr('is_active')|list|length }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-green-50 rounded-lg p-4">
                <div class="flex items-center">
                    <i class="fas fa-calendar text-green-600 text-xl mr-3"></i>
                    <div>
                        <p class="text-sm text-green-600">Lịch dạy</p>
                        <p class="text-xl font-bold text-green-800">{{ class_obj.schedules|selectattr('is_active')|list|length }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-purple-50 rounded-lg p-4">
                <div class="flex items-center">
                    <i class="fas fa-clock text-purple-600 text-xl mr-3"></i>
                    <div>
                        <p class="text-sm text-purple-600">Ngày tạo</p>
                        <p class="text-sm font-medium text-purple-800">{{ class_obj.created_at.strftime('%d/%m/%Y') if class_obj.created_at else 'N/A' }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('form');
    const nameInput = document.querySelector('input[name="name"]');
    
    form.addEventListener('submit', function(e) {
        const name = nameInput.value.trim();
        
        if (!name) {
            e.preventDefault();
            alert('Vui lòng nhập tên lớp');
            nameInput.focus();
            return false;
        }
    });
    
    // Auto-focus on name field
    nameInput.focus();
    nameInput.select();
});
</script>
{% endblock %}
