{% extends "base_tailwind.html" %}

{% block content %}
<!-- Header -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-plus text-orange-500 mr-3"></i>
                Thêm lớp học mới
            </h1>
            <p class="text-gray-600 mt-1">Tạo lớp học mới trong hệ thống</p>
        </div>
        
        <div class="flex space-x-2">
            <a href="{{ url_for('manager.classes') }}" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-arrow-left mr-2"></i>
                Quay lại
            </a>
        </div>
    </div>
</div>

<!-- Form -->
<div class="bg-white rounded-lg shadow-md p-6">
    <form method="POST" class="space-y-6">
        {{ form.hidden_tag() }}
        
        <!-- Basic Information -->
        <div class="border-b border-gray-200 pb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Thông tin cơ bản</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-chalkboard text-blue-500 mr-1"></i>
                        Tên lớp học *
                    </label>
                    {{ form.name(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500", placeholder="Nhập tên lớp học") }}
                    {% if form.name.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.name.errors[0] }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user-tie text-green-500 mr-1"></i>
                        Quản sinh
                    </label>
                    {{ form.manager_id(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500") }}
                    {% if form.manager_id.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.manager_id.errors[0] }}</p>
                    {% endif %}
                </div>
                

            </div>
            
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-align-left text-gray-500 mr-1"></i>
                    Mô tả
                </label>
                {{ form.description(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500", rows="4", placeholder="Mô tả về lớp học...") }}
                {% if form.description.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.description.errors[0] }}</p>
                {% endif %}
            </div>
        </div>
        
        <!-- Status -->
        <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Trạng thái</h3>
            <div class="flex items-center">
                {{ form.is_active(class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded") }}
                <label for="{{ form.is_active.id }}" class="ml-2 block text-sm text-gray-900">
                    Kích hoạt lớp học
                </label>
            </div>
        </div>
        
        <!-- Submit Buttons -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <a href="{{ url_for('manager.classes') }}" 
               class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                <i class="fas fa-times mr-2"></i>
                Hủy
            </a>
            <button type="submit" 
                    class="px-6 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors duration-200">
                <i class="fas fa-save mr-2"></i>
                Tạo lớp học
            </button>
        </div>
    </form>
</div>

<!-- Guidelines -->
<div class="bg-white rounded-lg shadow-md p-6 mt-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
        Hướng dẫn
    </h3>
    <ul class="space-y-2 text-sm text-gray-600">
        <li class="flex items-start">
            <i class="fas fa-check-circle text-green-500 mr-2 mt-0.5"></i>
            <span>Tên lớp học nên ngắn gọn và dễ nhớ</span>
        </li>
        <li class="flex items-start">
            <i class="fas fa-check-circle text-green-500 mr-2 mt-0.5"></i>
            <span>Có thể chọn quản sinh phụ trách lớp học này</span>
        </li>
        <li class="flex items-start">
            <i class="fas fa-check-circle text-green-500 mr-2 mt-0.5"></i>
            <span>Sĩ số tối đa giúp kiểm soát số lượng học sinh</span>
        </li>
        <li class="flex items-start">
            <i class="fas fa-check-circle text-green-500 mr-2 mt-0.5"></i>
            <span>Có thể chỉnh sửa thông tin lớp học sau khi tạo</span>
        </li>
    </ul>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus first input
    const firstInput = document.querySelector('input[name="name"]');
    if (firstInput) {
        firstInput.focus();
    }
    

    
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const name = document.querySelector('input[name="name"]').value.trim();
        if (!name) {
            e.preventDefault();
            alert('Vui lòng nhập tên lớp học');
            return false;
        }
        

    });
});
</script>
{% endblock %}
