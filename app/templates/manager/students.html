{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="fas fa-user-graduate"></i> <PERSON><PERSON><PERSON><PERSON> lý học sinh</h2>
            <a href="{{ url_for('manager.create_student') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Thêm học sinh
            </a>
        </div>
        <hr>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-4">
        <input type="text" id="searchInput" class="form-control" placeholder="Tìm kiếm học sinh...">
    </div>
    <div class="col-md-4">
        <select class="form-select" id="classFilter">
            <option value="">Tất cả lớp</option>
            {% for student in students.items %}
                {% if student.class_obj and student.class_obj.name not in class_names %}
                    {% set _ = class_names.append(student.class_obj.name) %}
                {% endif %}
            {% endfor %}
            {% for class_name in class_names|unique %}
            <option value="{{ class_name }}">{{ class_name }}</option>
            {% endfor %}
        </select>
    </div>
    <div class="col-md-4">
        <button class="btn btn-outline-success" onclick="exportTableToCSV('studentsTable', 'danh_sach_hoc_sinh.csv')">
            <i class="fas fa-file-excel"></i> Xuất Excel
        </button>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="studentsTable">
                        <thead>
                            <tr>
                                <th>STT</th>
                                <th>Mã HS</th>
                                <th>Họ và tên</th>
                                <th>Ngày sinh</th>
                                <th>Lớp</th>
                                <th>Phụ huynh</th>
                                <th>SĐT phụ huynh</th>
                                <th>Chuyên cần</th>
                                <th>Hồ sơ</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in students.items %}
                            <tr>
                                <td>{{ loop.index + (students.page - 1) * students.per_page }}</td>
                                <td><strong>{{ student.student_code }}</strong></td>
                                <td>{{ student.full_name }}</td>
                                <td>
                                    {% if student.date_of_birth %}
                                        {{ student.date_of_birth.strftime('%d/%m/%Y') }}
                                    {% else %}
                                        <span class="text-muted">Chưa có</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if student.class_obj %}
                                        <span class="badge bg-info">{{ student.class_obj.name }}</span>
                                        <small class="d-block text-muted">{{ student.class_obj.block_name }}</small>
                                    {% else %}
                                        <span class="text-muted">Chưa phân lớp</span>
                                    {% endif %}
                                </td>
                                <td>{{ student.parent_name or '-' }}</td>
                                <td>{{ student.parent_phone or '-' }}</td>
                                <td>
                                    {% set attendance_rate = student.attendance_rate %}
                                    {% if attendance_rate >= 90 %}
                                        <span class="badge bg-success">{{ attendance_rate }}%</span>
                                    {% elif attendance_rate >= 75 %}
                                        <span class="badge bg-warning">{{ attendance_rate }}%</span>
                                    {% else %}
                                        <span class="badge bg-danger">{{ attendance_rate }}%</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if student.profile_url %}
                                        <a href="{{ student.profile_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-external-link-alt"></i>
                                        </a>
                                    {% else %}
                                        <span class="text-muted">Chưa có</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-info" title="Xem chi tiết"
                                                onclick="viewStudentDetails({{ student.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <a href="{{ url_for('manager.edit_student', student_id=student.id) }}"
                                           class="btn btn-outline-primary" title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-outline-warning" title="Lịch sử điểm danh"
                                                onclick="viewAttendanceHistory({{ student.id }})">
                                            <i class="fas fa-history"></i>
                                        </button>
                                        <form method="POST" action="{{ url_for('manager.delete_student', student_id=student.id) }}"
                                              style="display: inline;" onsubmit="return confirmDelete('Bạn có chắc chắn muốn xóa học sinh {{ student.full_name }}?')">
                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                            <button type="submit" class="btn btn-outline-danger" title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if students.pages > 1 %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if students.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('manager.students', page=students.prev_num) }}">Trước</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in students.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != students.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('manager.students', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if students.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('manager.students', page=students.next_num) }}">Sau</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Student Details Modal -->
<div class="modal fade" id="studentDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Chi tiết học sinh</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="studentDetailsContent">
                <!-- Content will be loaded via AJAX -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" onclick="editStudent()">Chỉnh sửa</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    searchTable('searchInput', 'studentsTable');
    
    // Class filter
    document.getElementById('classFilter').addEventListener('change', function() {
        var filter = this.value.toLowerCase();
        var table = document.getElementById('studentsTable');
        var rows = table.getElementsByTagName('tr');
        
        for (var i = 1; i < rows.length; i++) {
            var classCell = rows[i].getElementsByTagName('td')[4];
            if (classCell) {
                var classText = classCell.textContent.toLowerCase();
                if (filter === '' || classText.includes(filter)) {
                    rows[i].style.display = '';
                } else {
                    rows[i].style.display = 'none';
                }
            }
        }
    });
});

function viewStudentDetails(studentId) {
    // Load student details via AJAX
    fetch(`/manager/student/${studentId}/details`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('studentDetailsContent').innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Thông tin cơ bản</h6>
                        <p><strong>Mã học sinh:</strong> ${data.student_code}</p>
                        <p><strong>Họ và tên:</strong> ${data.full_name}</p>
                        <p><strong>Ngày sinh:</strong> ${data.date_of_birth || 'Chưa có'}</p>
                        <p><strong>Lớp:</strong> ${data.class_name || 'Chưa phân lớp'}</p>
                        <p><strong>Địa chỉ:</strong> ${data.address || 'Chưa có'}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>Thông tin phụ huynh</h6>
                        <p><strong>Tên phụ huynh:</strong> ${data.parent_name || 'Chưa có'}</p>
                        <p><strong>Số điện thoại:</strong> ${data.parent_phone || 'Chưa có'}</p>
                        <p><strong>Hồ sơ:</strong> 
                            ${data.profile_url ? 
                                `<a href="${data.profile_url}" target="_blank">Xem hồ sơ</a>` : 
                                'Chưa có'
                            }
                        </p>
                        <p><strong>Tỷ lệ chuyên cần:</strong> 
                            <span class="badge ${data.attendance_rate >= 90 ? 'bg-success' : 
                                                data.attendance_rate >= 75 ? 'bg-warning' : 'bg-danger'}">
                                ${data.attendance_rate}%
                            </span>
                        </p>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-12">
                        <h6>Lịch sử điểm danh gần đây</h6>
                        ${data.recent_attendance.length > 0 ? 
                            `<div class="table-responsive">
                                <table class="table table-sm">
                                    <thead><tr><th>Ngày</th><th>Môn học</th><th>Trạng thái</th><th>Ghi chú</th></tr></thead>
                                    <tbody>
                                        ${data.recent_attendance.map(a => 
                                            `<tr>
                                                <td>${a.date}</td>
                                                <td>${a.subject || 'N/A'}</td>
                                                <td><span class="badge ${a.status === 'present' ? 'bg-success' : 'bg-warning'}">${a.status_display}</span></td>
                                                <td>${a.reason || '-'}</td>
                                            </tr>`
                                        ).join('')}
                                    </tbody>
                                </table>
                            </div>` :
                            '<p class="text-muted">Chưa có dữ liệu điểm danh</p>'
                        }
                    </div>
                </div>
            `;
            var modal = new bootstrap.Modal(document.getElementById('studentDetailsModal'));
            modal.show();
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'Không thể tải thông tin học sinh');
        });
}
</script>
{% endblock %}
