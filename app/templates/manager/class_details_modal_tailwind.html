<!-- Class Details -->
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-xl font-bold mb-2">{{ class_obj.name }}</h3>
                <p class="text-blue-100">{{ class_obj.description or 'Không có mô tả' }}</p>
            </div>
            <div class="text-right">
                <div class="text-2xl font-bold">{{ students_count }}</div>
                <div class="text-blue-200 text-sm">Học sinh</div>
            </div>
        </div>
    </div>

    <!-- Basic Information -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                Thông tin cơ bản
            </h4>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-600">Tên lớp:</span>
                    <span class="font-medium">{{ class_obj.name }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Sĩ số:</span>
                    <span class="font-medium">{{ students_count }} học sinh</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Trạng thái:</span>
                    <span class="font-medium">
                        {% if class_obj.is_active %}
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                            <i class="fas fa-check-circle mr-1"></i> Hoạt động
                        </span>
                        {% else %}
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
                            <i class="fas fa-times-circle mr-1"></i> Không hoạt động
                        </span>
                        {% endif %}
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Ngày tạo:</span>
                    <span class="font-medium">{{ class_obj.created_at.strftime('%d/%m/%Y') if class_obj.created_at else 'Không có thông tin' }}</span>
                </div>
            </div>
        </div>

        <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                <i class="fas fa-user-tie text-green-500 mr-2"></i>
                Quản sinh
            </h4>
            <div class="space-y-2 text-sm">
                {% if class_obj.manager %}
                <div class="flex justify-between">
                    <span class="text-gray-600">Họ tên:</span>
                    <span class="font-medium">{{ class_obj.manager.full_name }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Email:</span>
                    <span class="font-medium">{{ class_obj.manager.email }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Điện thoại:</span>
                    <span class="font-medium">{{ class_obj.manager.phone or 'Chưa cập nhật' }}</span>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-user-slash text-gray-400 text-2xl mb-2"></i>
                    <p class="text-gray-500">Chưa phân công quản sinh</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Schedule Information -->
    <div class="bg-white border border-gray-200 rounded-lg">
        <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
            <h4 class="font-semibold text-gray-900 flex items-center">
                <i class="fas fa-calendar-alt text-purple-500 mr-2"></i>
                Lịch dạy ({{ schedules|length }} buổi/tuần)
            </h4>
        </div>
        <div class="p-4">
            {% if schedules %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                {% for schedule in schedules %}
                <div class="border border-gray-200 rounded-lg p-3 hover:bg-gray-50 transition-colors duration-200">
                    <div class="flex items-center justify-between mb-2">
                        <span class="font-medium text-gray-900">
                            {% set days = ['', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật'] %}
                            {{ days[schedule.day_of_week] }}
                        </span>
                        <span class="text-sm text-gray-500">{{ schedule.time_range }}</span>
                    </div>
                    <div class="text-sm text-gray-600">
                        <div class="flex items-center mb-1">
                            <i class="fas fa-user-tie mr-2 text-gray-400"></i>
                            {{ schedule.teacher.full_name }}
                        </div>
                        {% if schedule.subject %}
                        <div class="flex items-center mb-1">
                            <i class="fas fa-book mr-2 text-gray-400"></i>
                            {{ schedule.subject }}
                        </div>
                        {% endif %}
                        {% if schedule.room %}
                        <div class="flex items-center">
                            <i class="fas fa-door-open mr-2 text-gray-400"></i>
                            {{ schedule.room }}
                        </div>
                        {% endif %}
                    </div>
                    <div class="mt-2">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            {% if schedule.session == 'morning' %}bg-blue-100 text-blue-800
                            {% else %}bg-green-100 text-green-800{% endif %}">
                            {{ schedule.session_name }}
                        </span>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-8">
                <i class="fas fa-calendar-times text-gray-400 text-3xl mb-3"></i>
                <p class="text-gray-500 mb-3">Chưa có lịch dạy nào</p>
                {% if current_user.is_admin() or current_user.is_manager() %}
                <a href="{{ url_for('manager.schedule') }}"
                   class="inline-flex items-center px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i>
                    Quản lý lịch dạy
                </a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-blue-50 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-blue-600">{{ schedules|length }}</div>
            <div class="text-sm text-blue-700">Buổi học/tuần</div>
        </div>
        <div class="bg-green-50 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-green-600">{{ students_count }}</div>
            <div class="text-sm text-green-700">Học sinh</div>
        </div>
        <div class="bg-purple-50 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-purple-600">
                {{ schedules|selectattr('teacher')|map(attribute='teacher')|unique|list|length }}
            </div>
            <div class="text-sm text-purple-700">Giáo viên</div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-wrap gap-3 pt-4 border-t border-gray-200">
        <a href="{{ url_for('manager.students', class=class_obj.id) }}" 
           class="flex-1 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 text-center">
            <i class="fas fa-users mr-2"></i>
            Quản lý học sinh
        </a>
        
        {% if current_user.is_admin() or current_user.is_manager() %}
        <button onclick="notify.info('Tính năng đang phát triển')"
                class="flex-1 bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 text-center">
            <i class="fas fa-edit mr-2"></i>
            Chỉnh sửa
        </button>
        {% endif %}
        
        <a href="{{ url_for('manager.schedule') }}"
           class="flex-1 bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 text-center">
            <i class="fas fa-calendar-plus mr-2"></i>
            Lịch dạy
        </a>
    </div>

    <!-- Notes Section -->
    {% if class_obj.notes %}
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h4 class="font-semibold text-yellow-800 mb-2 flex items-center">
            <i class="fas fa-sticky-note mr-2"></i>
            Ghi chú
        </h4>
        <p class="text-yellow-700 text-sm">{{ class_obj.notes }}</p>
    </div>
    {% endif %}
</div>
