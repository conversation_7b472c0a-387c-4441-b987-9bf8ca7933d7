{% extends "base_tailwind.html" %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ title }}</h1>
                <p class="text-gray-600 mt-1">Thông tin chi tiết về lớp học</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ url_for('manager.add_student_to_class', class_id=class_obj.id) }}" 
                   class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                    <i class="fas fa-user-plus mr-2"></i>
                    Thêm học sinh
                </a>
                <a href="{{ url_for('manager.classes') }}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Quay lại
                </a>
            </div>
        </div>
    </div>

    <!-- Class Info Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <!-- Total Students -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Tổng học sinh</p>
                    <p class="text-2xl font-bold text-gray-900">{{ class_obj.student_count or 0 }}</p>
                </div>
            </div>
        </div>

        <!-- Active Students -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-check text-green-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Đang học</p>
                    <p class="text-2xl font-bold text-gray-900">{{ class_obj.students|selectattr('is_active')|list|length }}</p>
                </div>
            </div>
        </div>

        <!-- Schedules -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-calendar text-purple-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Lịch dạy</p>
                    <p class="text-2xl font-bold text-gray-900">{{ class_obj.schedules|selectattr('is_active')|list|length }}</p>
                </div>
            </div>
        </div>

        <!-- Status -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 {{ 'bg-green-100' if class_obj.is_active else 'bg-red-100' }} rounded-lg flex items-center justify-center">
                        <i class="fas {{ 'fa-check-circle text-green-600' if class_obj.is_active else 'fa-times-circle text-red-600' }}"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Trạng thái</p>
                    <p class="text-lg font-bold {{ 'text-green-600' if class_obj.is_active else 'text-red-600' }}">
                        {{ 'Hoạt động' if class_obj.is_active else 'Không hoạt động' }}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Class Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Basic Info -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Thông tin cơ bản</h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600">Tên lớp:</span>
                    <span class="font-medium text-gray-900">{{ class_obj.name }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Mô tả:</span>
                    <span class="font-medium text-gray-900">{{ class_obj.description or 'Không có mô tả' }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Quản sinh:</span>
                    <span class="font-medium text-gray-900">{{ class_obj.manager.full_name if class_obj.manager else 'Chưa phân công' }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Ngày tạo:</span>
                    <span class="font-medium text-gray-900">{{ class_obj.created_at.strftime('%d/%m/%Y') if class_obj.created_at else 'Không có thông tin' }}</span>
                </div>
            </div>
        </div>

        <!-- Schedule Summary -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Lịch dạy</h3>
            {% set schedules = class_obj.schedules|selectattr('is_active')|list %}
            {% if schedules %}
            <div class="space-y-3">
                {% for schedule in schedules %}
                <div class="border border-gray-200 rounded-lg p-3">
                    <div class="flex justify-between items-start">
                        <div>
                            <p class="font-medium text-gray-900">{{ schedule.subject or 'Chưa có môn học' }}</p>
                            <p class="text-sm text-gray-600">
                                {{ schedule.day_name }} - {{ schedule.start_time.strftime('%H:%M') }} đến {{ schedule.end_time.strftime('%H:%M') }}
                            </p>
                            <p class="text-sm text-gray-500">GV: {{ schedule.teacher.full_name }}</p>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                   {{ 'bg-green-100 text-green-800' if schedule.session == 'morning' 
                                      else 'bg-blue-100 text-blue-800' if schedule.session == 'afternoon'
                                      else 'bg-purple-100 text-purple-800' }}">
                            {{ schedule.session_name }}
                        </span>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-8">
                <i class="fas fa-calendar-times text-gray-400 text-3xl mb-3"></i>
                <p class="text-gray-500">Chưa có lịch dạy nào</p>
                <a href="{{ url_for('manager.schedule') }}" 
                   class="inline-flex items-center mt-3 text-orange-600 hover:text-orange-700">
                    <i class="fas fa-plus mr-1"></i>
                    Tạo lịch dạy
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Students List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Danh sách học sinh</h3>
                <a href="{{ url_for('manager.add_student_to_class', class_id=class_obj.id) }}" 
                   class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                    <i class="fas fa-plus mr-1"></i>
                    Thêm học sinh
                </a>
            </div>
        </div>
        
        <div class="p-6">
            {% set students = class_obj.students|selectattr('is_active')|list %}
            {% if students %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {% for student in students %}
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900">{{ student.full_name }}</h4>
                            <p class="text-sm text-gray-600">{{ student.student_code }}</p>
                            {% if student.parent_name %}
                            <p class="text-sm text-gray-500 mt-1">
                                <i class="fas fa-user mr-1"></i>
                                {{ student.parent_name }}
                            </p>
                            {% endif %}
                            {% if student.parent_phone %}
                            <p class="text-sm text-gray-500">
                                <i class="fas fa-phone mr-1"></i>
                                {{ student.parent_phone }}
                            </p>
                            {% endif %}
                        </div>
                        <div class="flex space-x-1">
                            <a href="{{ url_for('manager.edit_student', student_id=student.id) }}" 
                               class="text-orange-600 hover:text-orange-700 p-1" title="Chỉnh sửa">
                                <i class="fas fa-edit text-sm"></i>
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-12">
                <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                <h4 class="text-lg font-medium text-gray-900 mb-2">Chưa có học sinh nào</h4>
                <p class="text-gray-600 mb-4">Thêm học sinh vào lớp để bắt đầu quản lý</p>
                <a href="{{ url_for('manager.add_student_to_class', class_id=class_obj.id) }}" 
                   class="inline-flex items-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                    <i class="fas fa-user-plus mr-2"></i>
                    Thêm học sinh đầu tiên
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Add any JavaScript functionality here if needed
document.addEventListener('DOMContentLoaded', function() {
    // Animation on load
    const cards = document.querySelectorAll('.bg-white');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.3s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
