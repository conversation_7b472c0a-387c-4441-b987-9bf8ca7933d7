<!-- Schedule Form -->
<form id="scheduleForm" method="POST" action="/manager/schedule/create" class="space-y-6">
    {{ form.hidden_tag() }}
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Class Selection -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-chalkboard text-orange-500 mr-1"></i>
                Lớ<PERSON> học *
            </label>
            {{ form.class_id(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500") }}
            {% if form.class_id.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.class_id.errors[0] }}</p>
            {% endif %}
        </div>

        <!-- Teacher Selection -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-user-tie text-blue-500 mr-1"></i>
                Giáo viên *
            </label>
            {{ form.teacher_id(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500") }}
            {% if form.teacher_id.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.teacher_id.errors[0] }}</p>
            {% endif %}
        </div>

        <!-- Week Selection -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-calendar-week text-blue-500 mr-1"></i>
                {{ form.week_number.label.text }} *
            </label>
            {{ form.week_number(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500") }}
            {% if form.week_number.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.week_number.errors[0] }}</p>
            {% endif %}
            <p class="mt-1 text-xs text-gray-500">Chọn tuần cụ thể để tạo lịch dạy</p>
        </div>

        <!-- Day of Week -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-calendar-day text-green-500 mr-1"></i>
                Thứ *
            </label>
            {{ form.day_of_week(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500") }}
            {% if form.day_of_week.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.day_of_week.errors[0] }}</p>
            {% endif %}
        </div>

        <!-- Session -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-sun text-yellow-500 mr-1"></i>
                Khung giờ *
            </label>
            {{ form.session(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500") }}
            {% if form.session.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.session.errors[0] }}</p>
            {% endif %}
        </div>

        <!-- Time Slot Selection -->
        <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-clock text-purple-500 mr-1"></i>
                Khung giờ *
            </label>
            <select id="timeSlotSelect" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                <option value="">Chọn khung giờ</option>
                <!-- Fallback options if JavaScript fails -->
                <option value="07:30-09:00">07:30 - 09:00</option>
                <option value="09:15-10:45">09:15 - 10:45</option>
                <option value="11:00-12:30">11:00 - 12:30</option>
                <option value="13:30-15:00">13:30 - 15:00</option>
                <option value="15:15-16:45">15:15 - 16:45</option>
                <option value="17:00-18:30">17:00 - 18:30</option>
                <option value="18:30-20:00">18:30 - 20:00</option>
                <option value="20:15-21:45">20:15 - 21:45</option>
            </select>
        </div>

        <!-- Start Time (Auto-filled) -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-clock text-purple-500 mr-1"></i>
                Giờ bắt đầu *
            </label>
            {{ form.start_time(class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:ring-2 focus:ring-orange-500 focus:border-orange-500", readonly=True) }}
            {% if form.start_time.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.start_time.errors[0] }}</p>
            {% endif %}
        </div>

        <!-- End Time (Auto-filled) -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-clock text-purple-500 mr-1"></i>
                Giờ kết thúc *
            </label>
            {{ form.end_time(class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:ring-2 focus:ring-orange-500 focus:border-orange-500", readonly=True) }}
            {% if form.end_time.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.end_time.errors[0] }}</p>
            {% endif %}
        </div>

        <!-- Subject -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-book text-indigo-500 mr-1"></i>
                Môn học
            </label>
            {{ form.subject(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500", placeholder="Nhập tên môn học") }}
            {% if form.subject.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.subject.errors[0] }}</p>
            {% endif %}
        </div>
    </div>

    <!-- Room -->
    <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
            <i class="fas fa-door-open text-teal-500 mr-1"></i>
            Phòng học
        </label>
        {{ form.room(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500", placeholder="Nhập phòng học") }}
        {% if form.room.errors %}
            <p class="mt-1 text-sm text-red-600">{{ form.room.errors[0] }}</p>
        {% endif %}
    </div>

    <!-- Notes -->
    <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
            <i class="fas fa-sticky-note text-yellow-500 mr-1"></i>
            Ghi chú
        </label>
        <textarea name="notes" rows="3" 
                  placeholder="Nhập ghi chú (tùy chọn)"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">{{ schedule.notes if schedule else '' }}</textarea>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button type="button" onclick="closeScheduleModal()" 
                class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200">
            <i class="fas fa-times mr-2"></i>
            Hủy
        </button>
        <button type="submit" 
                class="px-6 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors duration-200">
            <i class="fas fa-save mr-2"></i>
            {% if action == 'edit' %}Cập nhật{% else %}Tạo mới{% endif %}
        </button>
    </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sessionSelect = document.querySelector('select[name="session"]');
    const timeSlotSelect = document.getElementById('timeSlotSelect');
    const startTimeInput = document.querySelector('input[name="start_time"]');
    const endTimeInput = document.querySelector('input[name="end_time"]');

    console.log('DOM elements found:');
    console.log('sessionSelect:', sessionSelect);
    console.log('timeSlotSelect:', timeSlotSelect);
    console.log('startTimeInput:', startTimeInput);
    console.log('endTimeInput:', endTimeInput);

    // Predefined time slots
    const predefinedSlots = {
        'morning': [
            {'start': '07:30', 'end': '09:00'},
            {'start': '09:15', 'end': '10:45'},
            {'start': '11:00', 'end': '12:30'}
        ],
        'afternoon': [
            {'start': '13:30', 'end': '15:00'},
            {'start': '15:15', 'end': '16:45'},
            {'start': '17:00', 'end': '18:30'}
        ],
        'evening': [
            {'start': '18:30', 'end': '20:00'},
            {'start': '20:15', 'end': '21:45'},
            {'start': '19:00', 'end': '20:30'},
            {'start': '20:45', 'end': '22:15'}
        ]
    };

    console.log('Predefined slots loaded:', predefinedSlots);

    // Load time slots based on session
    function loadTimeSlots() {
        const selectedSession = sessionSelect.value;
        console.log('loadTimeSlots called, selectedSession:', selectedSession);

        if (!selectedSession) {
            timeSlotSelect.innerHTML = '<option value="">Chọn khung giờ</option>';
            startTimeInput.value = '';
            endTimeInput.value = '';
            return;
        }

        // Use predefined slots first
        if (predefinedSlots[selectedSession]) {
            console.log('Using predefined slots for:', selectedSession);
            loadPredefinedSlots(selectedSession);
            return;
        }

        // Fetch time slots from API
        fetch(`/manager/api/time-slots/${selectedSession}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(timeSlots => {
                timeSlotSelect.innerHTML = '<option value="">Chọn khung giờ</option>';

                timeSlots.forEach((slot, index) => {
                    const option = document.createElement('option');
                    option.value = `${slot.start}-${slot.end}`;
                    option.textContent = `${slot.start} - ${slot.end}`;
                    timeSlotSelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading time slots:', error);
                // Fallback to predefined slots
                loadPredefinedSlots(selectedSession);
            });
    }

    // Load predefined time slots
    function loadPredefinedSlots(session) {
        const slots = predefinedSlots[session] || [];
        console.log('loadPredefinedSlots called for session:', session, 'slots:', slots);
        timeSlotSelect.innerHTML = '<option value="">Chọn khung giờ</option>';

        slots.forEach((slot, index) => {
            const option = document.createElement('option');
            option.value = `${slot.start}-${slot.end}`;
            option.textContent = `${slot.start} - ${slot.end}`;
            timeSlotSelect.appendChild(option);
            console.log('Added option:', option.value, option.textContent);
        });
    }

    // Update start/end time when time slot is selected
    function updateTimes() {
        const selectedSlot = timeSlotSelect.value;
        console.log('updateTimes called with:', selectedSlot);
        if (selectedSlot && selectedSlot.includes('-')) {
            const [start, end] = selectedSlot.split('-');
            console.log('Setting times:', start, 'to', end);
            startTimeInput.value = start;
            endTimeInput.value = end;
        } else {
            console.log('Invalid slot format or empty slot');
        }
    }

    sessionSelect.addEventListener('change', function() {
        console.log('Session changed to:', sessionSelect.value);
        console.log('Available sessions in predefinedSlots:', Object.keys(predefinedSlots));
        console.log('Selected session exists in predefinedSlots:', sessionSelect.value in predefinedSlots);
        loadTimeSlots();
    });
    timeSlotSelect.addEventListener('change', function() {
        console.log('Time slot changed to:', timeSlotSelect.value);
        updateTimes();
    });

    // Initial load if session is already selected
    console.log('Initial session value:', sessionSelect.value);
    if (sessionSelect.value) {
        loadTimeSlots();
    }

    // Load all time slots immediately
    function loadAllTimeSlots() {
        timeSlotSelect.innerHTML = '<option value="">Chọn khung giờ</option>';

        // Add all time slots from all sessions
        const allSlots = [
            ...predefinedSlots.morning,
            ...predefinedSlots.afternoon,
            ...predefinedSlots.evening
        ];

        allSlots.forEach((slot, index) => {
            const option = document.createElement('option');
            option.value = `${slot.start}-${slot.end}`;
            option.textContent = `${slot.start} - ${slot.end}`;
            timeSlotSelect.appendChild(option);
        });
    }

    // Load all slots immediately
    loadAllTimeSlots();
    
    // Form submission
    document.getElementById('scheduleForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);

        // Add CSRF token if not already present
        const csrfToken = document.querySelector('input[name="csrf_token"]');
        if (csrfToken) {
            formData.set('csrf_token', csrfToken.value);
        }

        const submitButton = this.querySelector('button[type="submit"]');
        const originalText = submitButton.innerHTML;
        
        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Đang xử lý...';
        
        // Determine URL based on action
        let url = '/manager/schedule/create';
        {% if action == 'edit' %}
        url = '/manager/schedule/{{ schedule.id }}/edit';
        {% endif %}
        
        fetch(url, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.redirected) {
                // Handle redirect for create
                window.location.href = response.url;
                return;
            }
            return response.json();
        })
        .then(data => {
            if (data && data.success) {
                notify.success(data.message);
                closeScheduleModal();
                location.reload();
            } else if (data && data.message) {
                notify.error(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            notify.error('Có lỗi xảy ra khi xử lý yêu cầu');
        })
        .finally(() => {
            // Restore button state
            submitButton.disabled = false;
            submitButton.innerHTML = originalText;
        });
    });
});
</script>
