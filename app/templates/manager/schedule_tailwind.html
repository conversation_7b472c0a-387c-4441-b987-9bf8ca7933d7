{% extends "base_tailwind.html" %}

{% block content %}
<!-- Header -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-calendar-alt text-orange-500 mr-3"></i>
                Quản lý lịch dạy
            </h1>
            <p class="text-gray-600 mt-1">Quản lý thời khóa biểu và lịch dạy của các lớp học</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <button onclick="openCreateScheduleModal()"
                    class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-plus mr-2"></i>
                <PERSON>h<PERSON><PERSON> l<PERSON> d<PERSON>
            </button>
            <a href="{{ url_for('manager.copy_schedule') }}"
               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-copy mr-2"></i>
                Sao chép lịch
            </a>
            <a href="{{ url_for('manager.notification_generator') }}"
               class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-bell mr-2"></i>
                Tạo thông báo
            </a>
            <a href="{{ url_for('manager.time_slots') }}" 
               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-clock mr-2"></i>
                Khung giờ
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards - Responsive Design -->
<div class="grid grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-6 mb-6">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-3 lg:p-6 border-l-4 border-blue-500">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 lg:w-12 lg:h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-calendar-check text-blue-600 text-sm lg:text-xl"></i>
                </div>
            </div>
            <div class="ml-2 lg:ml-4">
                <p class="text-xs lg:text-sm font-medium text-gray-600">Tổng lịch dạy</p>
                <p class="text-lg lg:text-2xl font-bold text-gray-900">{{ schedules|length }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-3 lg:p-6 border-l-4 border-green-500">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 lg:w-12 lg:h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chalkboard text-green-600 text-sm lg:text-xl"></i>
                </div>
            </div>
            <div class="ml-2 lg:ml-4">
                <p class="text-xs lg:text-sm font-medium text-gray-600">Tổng lớp học</p>
                <p class="text-lg lg:text-2xl font-bold text-gray-900">{{ schedules|map(attribute='class_id')|unique|list|length }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-3 lg:p-6 border-l-4 border-purple-500">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 lg:w-12 lg:h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-user-tie text-purple-600 text-sm lg:text-xl"></i>
                </div>
            </div>
            <div class="ml-2 lg:ml-4">
                <p class="text-xs lg:text-sm font-medium text-gray-600">Giáo viên</p>
                <p class="text-lg lg:text-2xl font-bold text-gray-900">{{ schedules|map(attribute='teacher_id')|unique|list|length }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-3 lg:p-6 border-l-4 border-orange-500">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 lg:w-12 lg:h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-orange-600 text-sm lg:text-xl"></i>
                </div>
            </div>
            <div class="ml-2 lg:ml-4">
                <p class="text-xs lg:text-sm font-medium text-gray-600">Lịch hôm nay</p>
                <p class="text-lg lg:text-2xl font-bold text-gray-900">0</p>
            </div>
        </div>
    </div>
</div>

<!-- Filter and Search -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Lớp học</label>
            <select id="classFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                <option value="">Tất cả lớp</option>
                {% for class in classes %}
                <option value="{{ class.id }}">{{ class.name }}</option>
                {% endfor %}
            </select>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Giáo viên</label>
            <select id="teacherFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                <option value="">Tất cả giáo viên</option>
                {% for teacher in teachers %}
                <option value="{{ teacher.id }}">{{ teacher.full_name }}</option>
                {% endfor %}
            </select>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Thứ</label>
            <select id="dayFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                <option value="">Tất cả</option>
                <option value="1">Thứ 2</option>
                <option value="2">Thứ 3</option>
                <option value="3">Thứ 4</option>
                <option value="4">Thứ 5</option>
                <option value="5">Thứ 6</option>
                <option value="6">Thứ 7</option>
                <option value="7">Chủ nhật</option>
            </select>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Buổi</label>
            <select id="sessionFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                <option value="">Tất cả</option>
                <option value="morning">Buổi sáng</option>
                <option value="afternoon">Buổi chiều</option>
                <option value="evening">Buổi tối</option>
            </select>
        </div>
    </div>
</div>

<!-- Schedule Table -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Danh sách lịch dạy</h3>
    </div>
    
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Lớp học
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Giáo viên
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Thời gian
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Môn học
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Phòng
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Trạng thái
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Thao tác
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200" id="scheduleTableBody">
                {% for schedule in schedules %}
                <tr class="hover:bg-gray-50 transition-colors duration-200 schedule-row" 
                    data-class-id="{{ schedule.class_id }}"
                    data-teacher-id="{{ schedule.teacher_id }}"
                    data-day="{{ schedule.day_of_week }}"
                    data-session="{{ schedule.session }}">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                    <i class="fas fa-chalkboard text-blue-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">{{ schedule.class_obj.name }}</div>
                                <div class="text-sm text-gray-500">{{ schedule.class_obj.student_count }} học sinh</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ schedule.teacher.full_name }}</div>
                        <div class="text-sm text-gray-500">{{ schedule.teacher.email }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">
                            {% set days = ['', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật'] %}
                            {{ days[schedule.day_of_week] }}
                        </div>
                        <div class="text-sm text-gray-500">{{ schedule.time_range }}</div>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-1
                            {% if schedule.session == 'morning' %}bg-blue-100 text-blue-800
                            {% else %}bg-green-100 text-green-800{% endif %}">
                            {{ schedule.session_name }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ schedule.subject or 'Chưa xác định' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ schedule.room or 'Chưa xác định' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if schedule.is_active %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check-circle mr-1"></i>
                            Hoạt động
                        </span>
                        {% else %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <i class="fas fa-times-circle mr-1"></i>
                            Tạm dừng
                        </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex items-center justify-end space-x-2">
                            <button onclick="viewScheduleDetails({{ schedule.id }})" 
                                    class="text-blue-600 hover:text-blue-900 p-2 rounded-lg hover:bg-blue-50 transition-colors duration-200" 
                                    title="Xem chi tiết">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button onclick="editSchedule({{ schedule.id }})" 
                                    class="text-orange-600 hover:text-orange-900 p-2 rounded-lg hover:bg-orange-50 transition-colors duration-200" 
                                    title="Chỉnh sửa">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="deleteSchedule({{ schedule.id }}, '{{ schedule.class_obj.name }}', '{{ days[schedule.day_of_week] }}')" 
                                    class="text-red-600 hover:text-red-900 p-2 rounded-lg hover:bg-red-50 transition-colors duration-200" 
                                    title="Xóa">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        {% if not schedules %}
        <div class="text-center py-12">
            <i class="fas fa-calendar-times text-gray-400 text-4xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Chưa có lịch dạy nào</h3>
            <p class="text-gray-500 mb-4">Bắt đầu bằng cách tạo lịch dạy đầu tiên</p>
            <button onclick="openCreateScheduleModal()" 
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700">
                <i class="fas fa-plus mr-2"></i>
                Tạo lịch dạy
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- Create/Edit Schedule Modal -->
<div id="scheduleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-96 overflow-y-auto">
            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                <h3 id="scheduleModalTitle" class="text-lg font-semibold text-gray-900">Thêm lịch dạy</h3>
                <button onclick="closeScheduleModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="scheduleModalContent" class="p-6">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Filter functionality
document.getElementById('classFilter').addEventListener('change', filterSchedules);
document.getElementById('teacherFilter').addEventListener('change', filterSchedules);
document.getElementById('dayFilter').addEventListener('change', filterSchedules);
document.getElementById('sessionFilter').addEventListener('change', filterSchedules);

function filterSchedules() {
    const classFilter = document.getElementById('classFilter').value;
    const teacherFilter = document.getElementById('teacherFilter').value;
    const dayFilter = document.getElementById('dayFilter').value;
    const sessionFilter = document.getElementById('sessionFilter').value;
    
    const rows = document.querySelectorAll('.schedule-row');
    
    rows.forEach(row => {
        const classId = row.dataset.classId;
        const teacherId = row.dataset.teacherId;
        const day = row.dataset.day;
        const session = row.dataset.session;
        
        const matchesClass = !classFilter || classId === classFilter;
        const matchesTeacher = !teacherFilter || teacherId === teacherFilter;
        const matchesDay = !dayFilter || day === dayFilter;
        const matchesSession = !sessionFilter || session === sessionFilter;
        
        if (matchesClass && matchesTeacher && matchesDay && matchesSession) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function openCreateScheduleModal() {
    document.getElementById('scheduleModalTitle').textContent = 'Thêm lịch dạy';
    document.getElementById('scheduleModal').classList.remove('hidden');

    // Load create form
    fetch('/manager/schedule/create')
        .then(response => response.text())
        .then(html => {
            document.getElementById('scheduleModalContent').innerHTML = html;

            // Re-initialize JavaScript for the loaded form
            initializeScheduleForm();
        })
        .catch(error => {
            notify.error('Không thể tải form tạo lịch dạy');
        });
}

function editSchedule(scheduleId) {
    document.getElementById('scheduleModalTitle').textContent = 'Chỉnh sửa lịch dạy';
    document.getElementById('scheduleModal').classList.remove('hidden');

    // Load edit form
    fetch(`/manager/schedule/${scheduleId}/edit`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('scheduleModalContent').innerHTML = html;

            // Re-initialize JavaScript for the loaded form
            initializeScheduleForm();
        })
        .catch(error => {
            notify.error('Không thể tải form chỉnh sửa');
        });
}

function viewScheduleDetails(scheduleId) {
    // Redirect to calendar detail
    window.location.href = `/calendar/schedule/${scheduleId}/detail`;
}

function deleteSchedule(scheduleId, className, dayName) {
    if (confirm(`Bạn có chắc chắn muốn xóa lịch dạy "${className} - ${dayName}"?`)) {
        fetch(`/manager/schedule/${scheduleId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                notify.success('Xóa lịch dạy thành công');
                location.reload();
            } else {
                notify.error(data.message || 'Có lỗi xảy ra');
            }
        })
        .catch(error => {
            notify.error('Có lỗi xảy ra khi xóa lịch dạy');
        });
    }
}

function closeScheduleModal() {
    document.getElementById('scheduleModal').classList.add('hidden');
}

function initializeScheduleForm() {
    // Re-run the schedule form JavaScript
    const sessionSelect = document.querySelector('#scheduleModal select[name="session"]');
    const timeSlotSelect = document.querySelector('#scheduleModal #timeSlotSelect');
    const startTimeInput = document.querySelector('#scheduleModal input[name="start_time"]');
    const endTimeInput = document.querySelector('#scheduleModal input[name="end_time"]');

    if (!sessionSelect || !timeSlotSelect || !startTimeInput || !endTimeInput) {
        console.log('Schedule form elements not found');
        return;
    }

    console.log('Initializing schedule form in modal');

    // Predefined time slots
    const predefinedSlots = {
        'morning': [
            {'start': '07:30', 'end': '09:00'},
            {'start': '09:15', 'end': '10:45'},
            {'start': '11:00', 'end': '12:30'}
        ],
        'afternoon': [
            {'start': '13:30', 'end': '15:00'},
            {'start': '15:15', 'end': '16:45'},
            {'start': '17:00', 'end': '18:30'}
        ],
        'evening': [
            {'start': '18:30', 'end': '20:00'},
            {'start': '20:15', 'end': '21:45'}
        ]
    };

    // Load all time slots immediately
    function loadAllTimeSlots() {
        timeSlotSelect.innerHTML = '<option value="">Chọn khung giờ</option>';

        // Add all time slots from all sessions
        const allSlots = [
            ...predefinedSlots.morning,
            ...predefinedSlots.afternoon,
            ...predefinedSlots.evening
        ];

        allSlots.forEach((slot, index) => {
            const option = document.createElement('option');
            option.value = `${slot.start}-${slot.end}`;
            option.textContent = `${slot.start} - ${slot.end}`;
            timeSlotSelect.appendChild(option);
        });
    }

    // Update start/end time when time slot is selected
    function updateTimes() {
        const selectedSlot = timeSlotSelect.value;
        console.log('updateTimes called with:', selectedSlot);
        if (selectedSlot && selectedSlot.includes('-')) {
            const [start, end] = selectedSlot.split('-');
            console.log('Setting times:', start, 'to', end);
            startTimeInput.value = start;
            endTimeInput.value = end;
        }
    }

    // Add event listeners
    timeSlotSelect.addEventListener('change', updateTimes);

    // Load all slots immediately
    loadAllTimeSlots();
}

// Close modal when clicking outside
document.getElementById('scheduleModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeScheduleModal();
    }
});

// Animation on load
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('.schedule-row');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease-out';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 50);
    });
});
</script>
{% endblock %}
