{% extends "base_tailwind.html" %}

{% block content %}
<!-- Header -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-user-plus text-orange-500 mr-3"></i>
                Thêm học sinh mới
            </h1>
            <p class="text-gray-600 mt-1">Nhập thông tin học sinh để thêm vào hệ thống</p>
        </div>
        
        <a href="{{ url_for('manager.students') }}" 
           class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
            <i class="fas fa-arrow-left mr-2"></i>
            Quay lại
        </a>
    </div>
</div>

<!-- Form -->
<div class="bg-white rounded-lg shadow-md p-6">
    <form method="POST" id="createStudentForm" class="space-y-6">
        {{ form.hidden_tag() }}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Student Code -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-id-card text-blue-500 mr-1"></i>
                    Mã học sinh (Tự động)
                </label>
                {{ form.student_code(class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:ring-2 focus:ring-orange-500 focus:border-orange-500", placeholder="Mã sẽ được tự động tạo", readonly=true) }}
                <p class="mt-1 text-sm text-gray-500">
                    <i class="fas fa-magic mr-1"></i>
                    Mã học sinh sẽ được tự động cấp phát bằng hash bắt đầu từ 1000
                </p>
                <p class="mt-1 text-sm text-blue-600">
                    <i class="fas fa-lightbulb mr-1"></i>
                    Ví dụ: 1000, 1001, 1002... (dựa trên thuật toán hash)
                </p>
                {% if form.student_code.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.student_code.errors[0] }}</p>
                {% endif %}
            </div>

            <!-- Full Name -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-user text-green-500 mr-1"></i>
                    Họ và tên *
                </label>
                {{ form.full_name(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500", placeholder="Nhập họ và tên") }}
                {% if form.full_name.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.full_name.errors[0] }}</p>
                {% endif %}
            </div>

            <!-- Date of Birth -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-birthday-cake text-pink-500 mr-1"></i>
                    Ngày sinh
                </label>
                {{ form.date_of_birth(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500") }}
                {% if form.date_of_birth.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.date_of_birth.errors[0] }}</p>
                {% endif %}
            </div>

            <!-- Note about class assignment -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-start">
                    <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-2"></i>
                    <div>
                        <h4 class="text-sm font-medium text-blue-800">Thông tin về lớp học</h4>
                        <p class="text-sm text-blue-700 mt-1">
                            Học sinh sẽ được phân vào các lớp học thông qua lịch dạy.
                            Một học sinh có thể học nhiều môn ở nhiều khung giờ khác nhau.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Parent Name -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-users text-indigo-500 mr-1"></i>
                    Tên phụ huynh
                </label>
                {{ form.parent_name(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500", placeholder="Nhập tên phụ huynh") }}
                {% if form.parent_name.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.parent_name.errors[0] }}</p>
                {% endif %}
            </div>

            <!-- Parent Phone -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-phone text-teal-500 mr-1"></i>
                    Số điện thoại phụ huynh
                </label>
                {{ form.parent_phone(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500", placeholder="Nhập số điện thoại") }}
                {% if form.parent_phone.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.parent_phone.errors[0] }}</p>
                {% endif %}
            </div>
        </div>

        <!-- Address -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-map-marker-alt text-red-500 mr-1"></i>
                Địa chỉ
            </label>
            {{ form.address(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500", rows="3", placeholder="Nhập địa chỉ") }}
            {% if form.address.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.address.errors[0] }}</p>
            {% endif %}
        </div>

        <!-- Profile URL -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-link text-blue-500 mr-1"></i>
                Link hồ sơ (Google Drive)
            </label>
            {{ form.profile_url(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500", placeholder="Nhập link Google Drive") }}
            {% if form.profile_url.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.profile_url.errors[0] }}</p>
            {% endif %}
        </div>

        <!-- Active Status -->
        <div class="flex items-center">
            {{ form.is_active(class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded") }}
            <label for="{{ form.is_active.id }}" class="ml-2 block text-sm text-gray-900">
                <i class="fas fa-check-circle text-green-500 mr-1"></i>
                Kích hoạt học sinh
            </label>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end space-x-3">
            <a href="{{ url_for('manager.students') }}" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                Hủy
            </a>
            <button type="submit" 
                    class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-save mr-2"></i>
                Thêm học sinh
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('createStudentForm');
    
    // Form validation
    form.addEventListener('submit', function(e) {
        const fullName = document.querySelector('input[name="full_name"]').value.trim();

        if (!fullName) {
            e.preventDefault();
            alert('Vui lòng nhập họ và tên');
            return false;
        }

        // Student code validation removed - will be auto-generated
        // Class validation removed - students don't need to be assigned to classes initially
    });
    
    // Phone number validation
    const phoneInput = document.querySelector('input[name="parent_phone"]');
    if (phoneInput) {
        phoneInput.addEventListener('input', function() {
            // Only allow numbers, spaces, dashes, and plus
            this.value = this.value.replace(/[^\d\s\-\+]/g, '');
        });
    }
    
    // Student code validation
    const codeInput = document.querySelector('input[name="student_code"]');
    if (codeInput) {
        codeInput.addEventListener('input', function() {
            // Convert to uppercase and remove special characters
            this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
        });
    }
});
</script>
{% endblock %}
