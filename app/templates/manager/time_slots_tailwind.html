{% extends "base_tailwind.html" %}

{% block content %}
<!-- Header -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-clock text-orange-500 mr-3"></i>
                Quản lý khung giờ
            </h1>
            <p class="text-gray-600 mt-1">Thiết lập và quản lý các khung giờ học trong ngày</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <button onclick="openCreateTimeSlotModal()" 
                    class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-plus mr-2"></i>
                Thêm khung giờ
            </button>
            <a href="{{ url_for('manager.schedule') }}" 
               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-calendar-alt mr-2"></i>
                Lịch dạy
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-blue-600 text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Tổng khung giờ</p>
                <p class="text-2xl font-bold text-gray-900">{{ time_slots|length }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-sun text-green-600 text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Buổi sáng</p>
                <p class="text-2xl font-bold text-gray-900">{{ slots_by_session.get('morning', [])|length }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-purple-500">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-moon text-purple-600 text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Buổi chiều</p>
                <p class="text-2xl font-bold text-gray-900">{{ slots_by_session.get('afternoon', [])|length }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-orange-500">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-circle text-orange-600 text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Đang sử dụng</p>
                <p class="text-2xl font-bold text-gray-900">{{ time_slots|selectattr('is_active')|list|length }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Time Slots by Session -->
{% for session_type, session_slots in slots_by_session.items() %}
<div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            {% if session_type == 'morning' %}
                <i class="fas fa-sun text-yellow-500 mr-2"></i>
                Buổi sáng
            {% else %}
                <i class="fas fa-moon text-purple-500 mr-2"></i>
                Buổi chiều
            {% endif %}
            <span class="ml-2 text-sm font-normal text-gray-600">({{ session_slots|length }} khung giờ)</span>
        </h3>
    </div>
    
    <div class="p-6">
        {% if session_slots %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for slot in session_slots %}
            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors duration-200 time-slot-card">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-gray-900">{{ slot.name }}</h4>
                    {% if slot.is_active %}
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <i class="fas fa-check-circle mr-1"></i>
                        Hoạt động
                    </span>
                    {% else %}
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <i class="fas fa-times-circle mr-1"></i>
                        Tạm dừng
                    </span>
                    {% endif %}
                </div>
                
                <div class="space-y-2 text-sm text-gray-600">
                    <div class="flex items-center">
                        <i class="fas fa-clock text-blue-500 mr-2"></i>
                        <span>{{ slot.start_time.strftime('%H:%M') }} - {{ slot.end_time.strftime('%H:%M') }}</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-hourglass-half text-green-500 mr-2"></i>
                        <span>{{ slot.duration }} phút</span>
                    </div>
                    {% if slot.description %}
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-purple-500 mr-2 mt-0.5"></i>
                        <span class="text-xs">{{ slot.description }}</span>
                    </div>
                    {% endif %}
                </div>
                
                <!-- Usage Info -->
                <div class="mt-3 pt-3 border-t border-gray-200">
                    <div class="flex items-center justify-between text-xs text-gray-500">
                        <span>Đang sử dụng: {{ slot.schedules_count or 0 }} lịch</span>
                        <span>Tạo: {{ slot.created_at.strftime('%d/%m/%Y') if slot.created_at else 'N/A' }}</span>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="mt-3 flex space-x-2">
                    <button onclick="editTimeSlot({{ slot.id }})" 
                            class="flex-1 bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-xs transition-colors duration-200">
                        <i class="fas fa-edit mr-1"></i>
                        Sửa
                    </button>
                    <button onclick="toggleTimeSlot({{ slot.id }}, {{ slot.is_active|lower }})" 
                            class="flex-1 {% if slot.is_active %}bg-yellow-500 hover:bg-yellow-600{% else %}bg-green-500 hover:bg-green-600{% endif %} text-white px-3 py-2 rounded text-xs transition-colors duration-200">
                        <i class="fas fa-{% if slot.is_active %}pause{% else %}play{% endif %} mr-1"></i>
                        {% if slot.is_active %}Tạm dừng{% else %}Kích hoạt{% endif %}
                    </button>
                    <button onclick="deleteTimeSlot({{ slot.id }}, '{{ slot.name }}')" 
                            class="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded text-xs transition-colors duration-200">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-8">
            <i class="fas fa-clock text-gray-400 text-3xl mb-3"></i>
            <h4 class="text-lg font-medium text-gray-900 mb-2">
                Chưa có khung giờ {% if session_type == 'morning' %}buổi sáng{% else %}buổi chiều{% endif %}
            </h4>
            <p class="text-gray-500 mb-4">Thêm khung giờ đầu tiên cho buổi này</p>
            <button onclick="openCreateTimeSlotModal('{{ session_type }}')" 
                    class="inline-flex items-center px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i>
                Thêm khung giờ
            </button>
        </div>
        {% endif %}
    </div>
</div>
{% endfor %}

<!-- Create/Edit Time Slot Modal -->
<div id="timeSlotModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                <h3 id="timeSlotModalTitle" class="text-lg font-semibold text-gray-900">Thêm khung giờ</h3>
                <button onclick="closeTimeSlotModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="timeSlotModalContent" class="p-6">
                <!-- Form will be loaded here -->
                <form id="timeSlotForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tên khung giờ *</label>
                        <input type="text" name="name" required 
                               placeholder="VD: Tiết 1, Tiết 2..."
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Giờ bắt đầu *</label>
                            <input type="time" name="start_time" required 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Giờ kết thúc *</label>
                            <input type="time" name="end_time" required 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Buổi *</label>
                        <select name="session_type" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                            <option value="">Chọn buổi</option>
                            <option value="morning">Buổi sáng</option>
                            <option value="afternoon">Buổi chiều</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Mô tả</label>
                        <textarea name="description" rows="3" 
                                  placeholder="Mô tả khung giờ (tùy chọn)"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"></textarea>
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeTimeSlotModal()" 
                                class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                            Hủy
                        </button>
                        <button type="submit" 
                                class="px-6 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors duration-200">
                            <i class="fas fa-save mr-2"></i>
                            Lưu
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function openCreateTimeSlotModal(sessionType = '') {
    document.getElementById('timeSlotModalTitle').textContent = 'Thêm khung giờ';
    document.getElementById('timeSlotModal').classList.remove('hidden');
    
    // Reset form
    document.getElementById('timeSlotForm').reset();
    
    // Pre-select session type if provided
    if (sessionType) {
        document.querySelector('select[name="session_type"]').value = sessionType;
    }
    
    // Set form action for create
    document.getElementById('timeSlotForm').onsubmit = function(e) {
        e.preventDefault();
        submitTimeSlotForm('/manager/time_slots/add');
    };
}

function editTimeSlot(slotId) {
    document.getElementById('timeSlotModalTitle').textContent = 'Chỉnh sửa khung giờ';
    document.getElementById('timeSlotModal').classList.remove('hidden');
    
    // Load existing data
    fetch(`/manager/time_slots/${slotId}/edit`)
        .then(response => response.json())
        .then(data => {
            document.querySelector('input[name="name"]').value = data.name;
            document.querySelector('input[name="start_time"]').value = data.start_time;
            document.querySelector('input[name="end_time"]').value = data.end_time;
            document.querySelector('select[name="session_type"]').value = data.session_type;
            document.querySelector('textarea[name="description"]').value = data.description || '';
        })
        .catch(error => {
            notify.error('Không thể tải thông tin khung giờ');
        });
    
    // Set form action for edit
    document.getElementById('timeSlotForm').onsubmit = function(e) {
        e.preventDefault();
        submitTimeSlotForm(`/manager/time_slots/${slotId}/edit`);
    };
}

function submitTimeSlotForm(url) {
    const form = document.getElementById('timeSlotForm');
    const formData = new FormData(form);
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    
    // Show loading state
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Đang lưu...';
    
    fetch(url, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            notify.success(data.message);
            closeTimeSlotModal();
            location.reload();
        } else {
            notify.error(data.message || 'Có lỗi xảy ra');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        notify.error('Có lỗi xảy ra khi lưu khung giờ');
    })
    .finally(() => {
        // Restore button state
        submitButton.disabled = false;
        submitButton.innerHTML = originalText;
    });
}

function toggleTimeSlot(slotId, isActive) {
    const action = isActive ? 'tạm dừng' : 'kích hoạt';
    
    if (confirm(`Bạn có chắc chắn muốn ${action} khung giờ này?`)) {
        fetch(`/manager/time_slots/${slotId}/toggle`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                notify.success(data.message);
                location.reload();
            } else {
                notify.error(data.message || 'Có lỗi xảy ra');
            }
        })
        .catch(error => {
            notify.error('Có lỗi xảy ra khi cập nhật khung giờ');
        });
    }
}

function deleteTimeSlot(slotId, slotName) {
    if (confirm(`Bạn có chắc chắn muốn xóa khung giờ "${slotName}"?`)) {
        fetch(`/manager/time_slots/${slotId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                notify.success(data.message);
                location.reload();
            } else {
                notify.error(data.message || 'Có lỗi xảy ra');
            }
        })
        .catch(error => {
            notify.error('Có lỗi xảy ra khi xóa khung giờ');
        });
    }
}

function closeTimeSlotModal() {
    document.getElementById('timeSlotModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('timeSlotModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeTimeSlotModal();
    }
});

// Animation on load
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.time-slot-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.3s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});

// Auto-calculate duration when times change
document.addEventListener('DOMContentLoaded', function() {
    const startTimeInput = document.querySelector('input[name="start_time"]');
    const endTimeInput = document.querySelector('input[name="end_time"]');
    
    function calculateDuration() {
        if (startTimeInput.value && endTimeInput.value) {
            const start = new Date(`2000-01-01 ${startTimeInput.value}`);
            const end = new Date(`2000-01-01 ${endTimeInput.value}`);
            const duration = (end - start) / (1000 * 60); // minutes
            
            if (duration > 0) {
                // You can display duration somewhere if needed
                console.log(`Duration: ${duration} minutes`);
            }
        }
    }
    
    startTimeInput.addEventListener('change', calculateDuration);
    endTimeInput.addEventListener('change', calculateDuration);
});
</script>
{% endblock %}
