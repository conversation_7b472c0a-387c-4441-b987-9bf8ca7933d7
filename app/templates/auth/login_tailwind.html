<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đ<PERSON><PERSON> nhập - <PERSON><PERSON><PERSON><PERSON> lý lớp học</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#fff7ed',
                            100: '#ffedd5',
                            200: '#fed7aa',
                            300: '#fdba74',
                            400: '#fb923c',
                            500: '#f97316',
                            600: '#ea580c',
                            700: '#c2410c',
                            800: '#9a3412',
                            900: '#7c2d12',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-orange-50 to-orange-100 min-h-screen flex items-center justify-center">
    <!-- Background Pattern -->
    <div class="absolute inset-0 bg-white bg-opacity-50">
    </div>

    <!-- Login Container -->
    <div class="relative z-10 w-full max-w-md">
        <!-- Logo Section -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full shadow-lg mb-4">
                <i class="fas fa-graduation-cap text-white text-2xl"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Hệ thống Quản lý Lớp học</h1>
            <p class="text-gray-600">Đăng nhập để truy cập hệ thống</p>
        </div>

        <!-- Login Form -->
        <div class="bg-white rounded-2xl shadow-xl p-8">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                <div class="mb-6 space-y-2">
                    {% for category, message in messages %}
                    <div class="p-4 rounded-lg {% if category == 'error' %}bg-red-50 border border-red-200 text-red-700{% elif category == 'success' %}bg-green-50 border border-green-200 text-green-700{% elif category == 'warning' %}bg-yellow-50 border border-yellow-200 text-yellow-700{% else %}bg-blue-50 border border-blue-200 text-blue-700{% endif %}">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                {% if category == 'error' %}
                                <i class="fas fa-exclamation-circle"></i>
                                {% elif category == 'success' %}
                                <i class="fas fa-check-circle"></i>
                                {% elif category == 'warning' %}
                                <i class="fas fa-exclamation-triangle"></i>
                                {% else %}
                                <i class="fas fa-info-circle"></i>
                                {% endif %}
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium">{{ message }}</p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            {% endwith %}

            <form method="POST" class="space-y-6">
                {{ form.hidden_tag() }}
                
                <!-- Username Field -->
                <div>
                    {{ form.username.label(class="block text-sm font-medium text-gray-700 mb-2") }}
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-user text-gray-400"></i>
                        </div>
                        {{ form.username(class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200", placeholder="Nhập tên đăng nhập") }}
                    </div>
                    {% if form.username.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.username.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Password Field -->
                <div>
                    {{ form.password.label(class="block text-sm font-medium text-gray-700 mb-2") }}
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        {{ form.password(class="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200", placeholder="Nhập mật khẩu") }}
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <button type="button" id="toggle-password" class="text-gray-400 hover:text-gray-600 focus:outline-none">
                                <i class="fas fa-eye" id="eye-icon"></i>
                            </button>
                        </div>
                    </div>
                    {% if form.password.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.password.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Remember Me -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        {{ form.remember_me(class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded") }}
                        {{ form.remember_me.label(class="ml-2 block text-sm text-gray-700") }}
                    </div>
                    <div class="text-sm">
                        <a href="#" class="font-medium text-orange-600 hover:text-orange-500 transition-colors duration-200">
                            Quên mật khẩu?
                        </a>
                    </div>
                </div>

                <!-- Submit Button -->
                <div>
                    {{ form.submit(class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 transform hover:scale-105") }}
                </div>
            </form>

            <!-- System Features -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <h3 class="text-sm font-medium text-gray-700 mb-4 text-center">Tính năng hệ thống</h3>
                <div class="grid grid-cols-2 gap-2 text-xs mb-6">
                    <div class="text-center">
                        <i class="fas fa-calendar-alt text-orange-500 mb-1"></i>
                        <p class="text-gray-600">Quản lý lịch học</p>
                    </div>
                    <div class="text-center">
                        <i class="fas fa-users text-orange-500 mb-1"></i>
                        <p class="text-gray-600">Quản lý học sinh</p>
                    </div>
                    <div class="text-center">
                        <i class="fas fa-chart-bar text-orange-500 mb-1"></i>
                        <p class="text-gray-600">Báo cáo thống kê</p>
                    </div>
                    <div class="text-center">
                        <i class="fas fa-money-bill text-orange-500 mb-1"></i>
                        <p class="text-gray-600">Quản lý tài chính</p>
                    </div>
                </div>


            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8">
            <p class="text-sm text-gray-600">
                © 2025 Hệ thống Quản lý Lớp học Tình Thương Bàu Bàng
            </p>
            <p class="text-xs text-gray-500 mt-1">
                Phiên bản 1.0 - Dev Trung Thành
            </p>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Password toggle functionality
        document.getElementById('toggle-password').addEventListener('click', function() {
            const passwordField = document.getElementById('password');
            const eyeIcon = document.getElementById('eye-icon');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        });

        // Form validation and loading state
        document.querySelector('form').addEventListener('submit', function(e) {
            const submitButton = document.querySelector('input[type="submit"]');
            submitButton.value = 'Đang đăng nhập...';
            submitButton.disabled = true;
            submitButton.classList.add('opacity-75', 'cursor-not-allowed');
        });

        // Auto-focus on username field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });

        // Demo account quick login
        function quickLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            document.querySelector('form').submit();
        }

        // Add click handlers to demo accounts
        document.querySelectorAll('.bg-gray-50').forEach((element, index) => {
            element.style.cursor = 'pointer';
            element.addEventListener('click', function() {
                const accounts = [
                    ['admin', 'admin123'],
                    ['manager1', 'manager123'],
                    ['teacher1', 'teacher123']
                ];
                if (accounts[index]) {
                    quickLogin(accounts[index][0], accounts[index][1]);
                }
            });
            
            element.addEventListener('mouseenter', function() {
                this.classList.add('bg-gray-100');
            });
            
            element.addEventListener('mouseleave', function() {
                this.classList.remove('bg-gray-100');
            });
        });

        // Add floating animation to logo
        const logo = document.querySelector('.inline-flex.items-center.justify-center');
        setInterval(() => {
            logo.style.transform = 'translateY(-5px)';
            setTimeout(() => {
                logo.style.transform = 'translateY(0)';
            }, 1000);
        }, 3000);
        logo.style.transition = 'transform 1s ease-in-out';
    </script>
</body>
</html>
