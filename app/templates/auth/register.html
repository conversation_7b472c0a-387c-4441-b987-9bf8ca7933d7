{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header text-center">
                <h4><i class="fas fa-user-plus"></i> <PERSON><PERSON><PERSON> ký tà<PERSON>n</h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control") }}
                            {% for error in form.username.errors %}
                            <div class="text-danger small">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control") }}
                            {% for error in form.email.errors %}
                            <div class="text-danger small">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.full_name.label(class="form-label") }}
                            {{ form.full_name(class="form-control") }}
                            {% for error in form.full_name.errors %}
                            <div class="text-danger small">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.phone.label(class="form-label") }}
                            {{ form.phone(class="form-control") }}
                            {% for error in form.phone.errors %}
                            <div class="text-danger small">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.role.label(class="form-label") }}
                        {{ form.role(class="form-select") }}
                        {% for error in form.role.errors %}
                        <div class="text-danger small">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.password.label(class="form-label") }}
                            {{ form.password(class="form-control") }}
                            {% for error in form.password.errors %}
                            <div class="text-danger small">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.password2.label(class="form-label") }}
                            {{ form.password2(class="form-control") }}
                            {% for error in form.password2.errors %}
                            <div class="text-danger small">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <small class="text-muted">
                    Đã có tài khoản? 
                    <a href="{{ url_for('auth.login') }}">Đăng nhập tại đây</a>
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
