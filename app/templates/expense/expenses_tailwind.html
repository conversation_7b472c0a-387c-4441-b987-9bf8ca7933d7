{% extends "base_tailwind.html" %}

{% block content %}
<!-- Header -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-list text-orange-500 mr-3"></i>
                Danh sách chi tiêu
            </h1>
            <p class="text-gray-600 mt-1">Quản lý và theo dõi tất cả chi tiêu</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <a href="{{ url_for('expense.create_expense') }}" 
               class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-plus mr-2"></i>
                Thêm chi tiêu
            </a>
            <button onclick="exportExpenses()" 
                    class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-download mr-2"></i>
                Xuất Excel
            </button>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <form method="GET" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Từ ngày</label>
                <input type="date" name="start_date" value="{{ request.args.get('start_date', '') }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Đến ngày</label>
                <input type="date" name="end_date" value="{{ request.args.get('end_date', '') }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Danh mục</label>
                {{ filter_form.category_id(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500") }}
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Trạng thái</label>
                {{ filter_form.status(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500") }}
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Tìm kiếm</label>
                <div class="flex space-x-2">
                    <input type="text" name="search" value="{{ search }}" placeholder="Tìm kiếm..."
                           class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                    <button type="submit" 
                            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Expenses Table -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">
            Chi tiêu 
            {% if expenses.total %}
            ({{ expenses.total }} bản ghi)
            {% endif %}
        </h3>
    </div>
    
    {% if expenses.items %}
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Chi tiêu
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Số tiền
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Danh mục
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Trạng thái
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ngày
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Thao tác
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for expense in expenses.items %}
                <tr class="hover:bg-gray-50 transition-colors duration-200">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div>
                            <div class="text-sm font-medium text-gray-900">{{ expense.title }}</div>
                            <div class="text-sm text-gray-500">
                                {% if expense.vendor %}{{ expense.vendor }}{% else %}Không có nhà cung cấp{% endif %}
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-bold text-gray-900">{{ "{:,.0f}".format(expense.amount) }} ₫</div>
                        <div class="text-sm text-gray-500">{{ expense.payment_method_display }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                              style="background-color: {{ expense.category.color }}20; color: {{ expense.category.color }}">
                            {{ expense.category.name }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {% if expense.status == 'approved' %}bg-green-100 text-green-800
                            {% elif expense.status == 'pending' %}bg-yellow-100 text-yellow-800
                            {% else %}bg-red-100 text-red-800{% endif %}">
                            {% if expense.status == 'approved' %}
                                <i class="fas fa-check-circle mr-1"></i>
                            {% elif expense.status == 'pending' %}
                                <i class="fas fa-clock mr-1"></i>
                            {% else %}
                                <i class="fas fa-times-circle mr-1"></i>
                            {% endif %}
                            {{ expense.status_display }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ expense.expense_date.strftime('%d/%m/%Y') }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex items-center justify-end space-x-2">
                            <button onclick="viewExpenseDetails({{ expense.id }})" 
                                    class="text-blue-600 hover:text-blue-900 p-2 rounded-lg hover:bg-blue-50 transition-colors duration-200" 
                                    title="Xem chi tiết">
                                <i class="fas fa-eye"></i>
                            </button>
                            
                            {% if current_user.is_admin() or expense.created_by == current_user.id %}
                            <a href="{{ url_for('expense.edit_expense', id=expense.id) }}" 
                               class="text-orange-600 hover:text-orange-900 p-2 rounded-lg hover:bg-orange-50 transition-colors duration-200" 
                               title="Chỉnh sửa">
                                <i class="fas fa-edit"></i>
                            </a>
                            {% endif %}
                            
                            {% if current_user.is_admin() and expense.status == 'pending' %}
                            <button onclick="approveExpense({{ expense.id }})" 
                                    class="text-green-600 hover:text-green-900 p-2 rounded-lg hover:bg-green-50 transition-colors duration-200" 
                                    title="Duyệt">
                                <i class="fas fa-check"></i>
                            </button>
                            {% endif %}
                            
                            {% if current_user.is_admin() or expense.created_by == current_user.id %}
                            <button onclick="deleteExpense({{ expense.id }}, '{{ expense.title }}')" 
                                    class="text-red-600 hover:text-red-900 p-2 rounded-lg hover:bg-red-50 transition-colors duration-200" 
                                    title="Xóa">
                                <i class="fas fa-trash"></i>
                            </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    {% if expenses.pages > 1 %}
    <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div class="flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if expenses.has_prev %}
                <a href="{{ url_for('expense.expenses', page=expenses.prev_num, **request.args) }}" 
                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Trước
                </a>
                {% endif %}
                {% if expenses.has_next %}
                <a href="{{ url_for('expense.expenses', page=expenses.next_num, **request.args) }}" 
                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Sau
                </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Hiển thị <span class="font-medium">{{ expenses.per_page * (expenses.page - 1) + 1 }}</span>
                        đến <span class="font-medium">{{ expenses.per_page * expenses.page if expenses.page < expenses.pages else expenses.total }}</span>
                        trong tổng số <span class="font-medium">{{ expenses.total }}</span> chi tiêu
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        {% for page_num in expenses.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != expenses.page %}
                                <a href="{{ url_for('expense.expenses', page=page_num, **request.args) }}" 
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    {{ page_num }}
                                </a>
                                {% else %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-orange-50 text-sm font-medium text-orange-600">
                                    {{ page_num }}
                                </span>
                                {% endif %}
                            {% else %}
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                ...
                            </span>
                            {% endif %}
                        {% endfor %}
                    </nav>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% else %}
    <div class="text-center py-12">
        <i class="fas fa-receipt text-gray-400 text-4xl mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Chưa có chi tiêu nào</h3>
        <p class="text-gray-500 mb-4">Bắt đầu bằng cách thêm chi tiêu đầu tiên</p>
        <a href="{{ url_for('expense.create_expense') }}" 
           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700">
            <i class="fas fa-plus mr-2"></i>
            Thêm chi tiêu
        </a>
    </div>
    {% endif %}
</div>

<!-- Expense Details Modal -->
<div id="expenseDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-96 overflow-y-auto">
            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Chi tiết chi tiêu</h3>
                <button onclick="closeExpenseDetailsModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="expenseDetailsContent" class="p-6">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function viewExpenseDetails(expenseId) {
    document.getElementById('expenseDetailsModal').classList.remove('hidden');
    document.getElementById('expenseDetailsContent').innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin text-orange-500"></i> Đang tải...</div>';
    
    // Load expense details via AJAX
    fetch(`/expense/expense/${expenseId}/details`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('expenseDetailsContent').innerHTML = `
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Tiêu đề:</label>
                            <p class="text-sm text-gray-900">${data.title}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Số tiền:</label>
                            <p class="text-sm text-gray-900 font-bold">${data.amount} ₫</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Ngày chi tiêu:</label>
                            <p class="text-sm text-gray-900">${data.expense_date}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Danh mục:</label>
                            <p class="text-sm text-gray-900">${data.category}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Nhà cung cấp:</label>
                            <p class="text-sm text-gray-900">${data.vendor || 'Không có'}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Số hóa đơn:</label>
                            <p class="text-sm text-gray-900">${data.receipt_number || 'Không có'}</p>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Mô tả:</label>
                        <p class="text-sm text-gray-900">${data.description || 'Không có mô tả'}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Ghi chú:</label>
                        <p class="text-sm text-gray-900">${data.notes || 'Không có ghi chú'}</p>
                    </div>
                </div>
            `;
        })
        .catch(error => {
            document.getElementById('expenseDetailsContent').innerHTML = '<div class="text-center py-4 text-red-500"><i class="fas fa-exclamation-triangle"></i> Lỗi khi tải thông tin</div>';
        });
}

function closeExpenseDetailsModal() {
    document.getElementById('expenseDetailsModal').classList.add('hidden');
}

function approveExpense(expenseId) {
    if (confirm('Bạn có chắc chắn muốn duyệt chi tiêu này?')) {
        fetch(`/expense/expense/${expenseId}/approve`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({status: 'approved'})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                notify.success(data.message);
                location.reload();
            } else {
                notify.error(data.message || 'Có lỗi xảy ra');
            }
        })
        .catch(error => {
            notify.error('Có lỗi xảy ra khi duyệt chi tiêu');
        });
    }
}

function deleteExpense(expenseId, expenseTitle) {
    if (confirm(`Bạn có chắc chắn muốn xóa chi tiêu "${expenseTitle}"?`)) {
        fetch(`/expense/expense/${expenseId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                notify.success(data.message);
                location.reload();
            } else {
                notify.error(data.message || 'Có lỗi xảy ra');
            }
        })
        .catch(error => {
            notify.error('Có lỗi xảy ra khi xóa chi tiêu');
        });
    }
}

function exportExpenses() {
    window.location.href = '{{ url_for("expense.export_expenses") }}';
}

// Close modal when clicking outside
document.getElementById('expenseDetailsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeExpenseDetailsModal();
    }
});
</script>
{% endblock %}
