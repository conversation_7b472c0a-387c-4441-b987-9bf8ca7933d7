{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="fas fa-chalkboard"></i> <PERSON><PERSON><PERSON><PERSON> lý lớp học</h2>
            <a href="{{ url_for('admin.create_class') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> T<PERSON><PERSON> lớp học
            </a>
        </div>
        <hr>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-6">
        <input type="text" id="searchInput" class="form-control" placeholder="Tìm kiếm lớp học...">
    </div>
    <div class="col-md-6">
        <select class="form-select" id="blockFilter">
            <option value="">T<PERSON>t cả khối</option>
            {% for class in classes.items %}
                {% if class.block_name not in block_names %}
                    {% set _ = block_names.append(class.block_name) %}
                {% endif %}
            {% endfor %}
            {% for block in block_names|unique %}
            <option value="{{ block }}">{{ block }}</option>
            {% endfor %}
        </select>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="classesTable">
                        <thead>
                            <tr>
                                <th>STT</th>
                                <th>Tên lớp</th>
                                <th>Khối</th>
                                <th>Quản sinh</th>
                                <th>Sĩ số</th>
                                <th>Giáo viên</th>
                                <th>Trạng thái</th>
                                <th>Ngày tạo</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for class in classes.items %}
                            <tr>
                                <td>{{ loop.index + (classes.page - 1) * classes.per_page }}</td>
                                <td><strong>{{ class.name }}</strong></td>
                                <td><span class="badge bg-info">{{ class.block_name }}</span></td>
                                <td>
                                    {% if class.manager %}
                                        {{ class.manager.full_name }}
                                    {% else %}
                                        <span class="text-muted">Chưa phân công</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ class.student_count }} HS</span>
                                </td>
                                <td>
                                    {% if class.teachers %}
                                        {% for teacher in class.teachers[:2] %}
                                            <small class="d-block">{{ teacher.full_name }}</small>
                                        {% endfor %}
                                        {% if class.teachers|length > 2 %}
                                            <small class="text-muted">+{{ class.teachers|length - 2 }} khác</small>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">Chưa có</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if class.is_active %}
                                        <span class="badge bg-success">Hoạt động</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Vô hiệu hóa</span>
                                    {% endif %}
                                </td>
                                <td>{{ class.created_at.strftime('%d/%m/%Y') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-info" title="Xem chi tiết" 
                                                onclick="viewClassDetails({{ class.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-primary" title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" title="Quản lý giáo viên">
                                            <i class="fas fa-users"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" title="Xóa" 
                                                onclick="return confirmDelete('Bạn có chắc chắn muốn xóa lớp học này?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if classes.pages > 1 %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if classes.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.classes', page=classes.prev_num) }}">Trước</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in classes.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != classes.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.classes', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if classes.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.classes', page=classes.next_num) }}">Sau</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Class Details Modal -->
<div class="modal fade" id="classDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Chi tiết lớp học</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="classDetailsContent">
                <!-- Content will be loaded via AJAX -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    searchTable('searchInput', 'classesTable');
    
    // Block filter
    document.getElementById('blockFilter').addEventListener('change', function() {
        var filter = this.value.toLowerCase();
        var table = document.getElementById('classesTable');
        var rows = table.getElementsByTagName('tr');
        
        for (var i = 1; i < rows.length; i++) {
            var blockCell = rows[i].getElementsByTagName('td')[2];
            if (blockCell) {
                var blockText = blockCell.textContent.toLowerCase();
                if (filter === '' || blockText.includes(filter)) {
                    rows[i].style.display = '';
                } else {
                    rows[i].style.display = 'none';
                }
            }
        }
    });
});

function viewClassDetails(classId) {
    // Load class details via AJAX
    fetch(`/admin/class/${classId}/details`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('classDetailsContent').innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Thông tin cơ bản</h6>
                        <p><strong>Tên lớp:</strong> ${data.name}</p>
                        <p><strong>Khối:</strong> ${data.block_name}</p>
                        <p><strong>Quản sinh:</strong> ${data.manager_name || 'Chưa phân công'}</p>
                        <p><strong>Sĩ số:</strong> ${data.student_count} học sinh</p>
                        <p><strong>Mô tả:</strong> ${data.description || 'Không có'}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>Giáo viên</h6>
                        ${data.teachers.length > 0 ? 
                            data.teachers.map(t => `<p>• ${t.full_name} (${t.phone || 'N/A'})</p>`).join('') :
                            '<p class="text-muted">Chưa có giáo viên</p>'
                        }
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-12">
                        <h6>Học sinh</h6>
                        ${data.students.length > 0 ? 
                            `<div class="table-responsive">
                                <table class="table table-sm">
                                    <thead><tr><th>Mã HS</th><th>Họ tên</th><th>Phụ huynh</th></tr></thead>
                                    <tbody>
                                        ${data.students.map(s => 
                                            `<tr><td>${s.student_code}</td><td>${s.full_name}</td><td>${s.parent_name || 'N/A'}</td></tr>`
                                        ).join('')}
                                    </tbody>
                                </table>
                            </div>` :
                            '<p class="text-muted">Chưa có học sinh</p>'
                        }
                    </div>
                </div>
            `;
            var modal = new bootstrap.Modal(document.getElementById('classDetailsModal'));
            modal.show();
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'Không thể tải thông tin lớp học');
        });
}
</script>
{% endblock %}
