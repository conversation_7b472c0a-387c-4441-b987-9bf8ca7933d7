{% extends "base_tailwind.html" %}

{% block content %}
<!-- Header -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-user-edit text-orange-500 mr-3"></i>
                Chỉnh sửa người dùng
            </h1>
            <p class="text-gray-600 mt-1">Cập nhật thông tin người dùng: {{ user.full_name }}</p>
        </div>
        
        <div class="flex space-x-2">
            <a href="{{ url_for('admin.users') }}" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-arrow-left mr-2"></i>
                Quay lại
            </a>
        </div>
    </div>
</div>

<!-- Form -->
<div class="bg-white rounded-lg shadow-md p-6">
    <form method="POST" class="space-y-6">
        {{ form.hidden_tag() }}
        
        <!-- Personal Information -->
        <div class="border-b border-gray-200 pb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Thông tin cá nhân</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user text-blue-500 mr-1"></i>
                        Họ và tên *
                    </label>
                    {{ form.full_name(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500") }}
                    {% if form.full_name.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.full_name.errors[0] }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-envelope text-green-500 mr-1"></i>
                        Email *
                    </label>
                    {{ form.email(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500") }}
                    {% if form.email.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.email.errors[0] }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-phone text-purple-500 mr-1"></i>
                        Số điện thoại
                    </label>
                    {{ form.phone(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500") }}
                    {% if form.phone.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.phone.errors[0] }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar text-indigo-500 mr-1"></i>
                        Ngày sinh
                    </label>
                    {{ form.date_of_birth(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500") }}
                    {% if form.date_of_birth.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.date_of_birth.errors[0] }}</p>
                    {% endif %}
                </div>
            </div>
            
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-map-marker-alt text-red-500 mr-1"></i>
                    Địa chỉ
                </label>
                {{ form.address(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500", rows="3") }}
                {% if form.address.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.address.errors[0] }}</p>
                {% endif %}
            </div>
        </div>
        
        <!-- Account Information -->
        <div class="border-b border-gray-200 pb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Thông tin tài khoản</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user-circle text-blue-500 mr-1"></i>
                        Tên đăng nhập *
                    </label>
                    {{ form.username(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500") }}
                    {% if form.username.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.username.errors[0] }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user-tag text-orange-500 mr-1"></i>
                        Vai trò *
                    </label>
                    {{ form.role(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500") }}
                    {% if form.role.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.role.errors[0] }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Password Change -->
        <div class="border-b border-gray-200 pb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Đổi mật khẩu</h3>
            <p class="text-sm text-gray-600 mb-4">Để trống nếu không muốn thay đổi mật khẩu</p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-lock text-red-500 mr-1"></i>
                        Mật khẩu mới
                    </label>
                    {{ form.password(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500", placeholder="Nhập mật khẩu mới") }}
                    {% if form.password.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.password.errors[0] }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-lock text-red-500 mr-1"></i>
                        Xác nhận mật khẩu mới
                    </label>
                    {{ form.password_confirm(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500", placeholder="Nhập lại mật khẩu mới") }}
                    {% if form.password_confirm.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.password_confirm.errors[0] }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Status -->
        <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Trạng thái</h3>
            <div class="flex items-center">
                {{ form.is_active(class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded") }}
                <label for="{{ form.is_active.id }}" class="ml-2 block text-sm text-gray-900">
                    Kích hoạt tài khoản
                </label>
            </div>
        </div>
        
        <!-- Submit Buttons -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <a href="{{ url_for('admin.users') }}" 
               class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                <i class="fas fa-times mr-2"></i>
                Hủy
            </a>
            <button type="submit" 
                    class="px-6 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors duration-200">
                <i class="fas fa-save mr-2"></i>
                Cập nhật
            </button>
        </div>
    </form>
</div>

<!-- Account Info -->
<div class="bg-white rounded-lg shadow-md p-6 mt-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Thông tin tài khoản</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
        <div>
            <span class="text-gray-600">Ngày tạo:</span>
            <span class="font-medium">{{ user.created_at.strftime('%d/%m/%Y %H:%M') if user.created_at else 'Không có thông tin' }}</span>
        </div>
        <div>
            <span class="text-gray-600">Lần đăng nhập cuối:</span>
            <span class="font-medium">{{ user.last_login.strftime('%d/%m/%Y %H:%M') if user.last_login else 'Chưa đăng nhập' }}</span>
        </div>
        <div>
            <span class="text-gray-600">ID:</span>
            <span class="font-medium">#{{ user.id }}</span>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password validation
    const passwordInput = document.querySelector('input[name="password"]');
    const confirmInput = document.querySelector('input[name="password_confirm"]');
    
    function validatePassword() {
        const password = passwordInput.value;
        const confirm = confirmInput.value;
        
        // Check password match
        if (password && confirm && password !== confirm) {
            confirmInput.setCustomValidity('Mật khẩu không khớp');
        } else {
            confirmInput.setCustomValidity('');
        }
    }
    
    passwordInput.addEventListener('input', validatePassword);
    confirmInput.addEventListener('input', validatePassword);
});
</script>
{% endblock %}
