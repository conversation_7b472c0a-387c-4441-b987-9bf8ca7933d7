{% extends "base_tailwind.html" %}

{% block content %}
<!-- Header -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-users-cog text-orange-500 mr-3"></i>
                Quản trị người dùng
            </h1>
            <p class="text-gray-600 mt-1">Quản lý tài khoản người dùng trong hệ thống</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <a href="{{ url_for('admin.create_user') }}" 
               class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-user-plus mr-2"></i>
                Thê<PERSON> ng<PERSON>ời dùng
            </a>
            <button onclick="exportUsers()" 
                    class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-download mr-2"></i>
                Xuất Excel
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-blue-600 text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Tổng người dùng</p>
                <p class="text-2xl font-bold text-gray-900">{{ users.total if users.pages else users|length }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chalkboard-teacher text-green-600 text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Giáo viên</p>
                <p class="text-2xl font-bold text-gray-900">
                    {% if users.items %}
                        {{ users.items|selectattr('role', 'equalto', 'teacher')|list|length }}
                    {% else %}
                        {{ users|selectattr('role', 'equalto', 'teacher')|list|length }}
                    {% endif %}
                </p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-purple-500">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-user-tie text-purple-600 text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Quản sinh</p>
                <p class="text-2xl font-bold text-gray-900">
                    {% if users.items %}
                        {{ users.items|selectattr('role', 'equalto', 'manager')|list|length }}
                    {% else %}
                        {{ users|selectattr('role', 'equalto', 'manager')|list|length }}
                    {% endif %}
                </p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-orange-500">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-user-check text-orange-600 text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Đang hoạt động</p>
                <p class="text-2xl font-bold text-gray-900">
                    {% if users.items %}
                        {{ users.items|selectattr('is_active')|list|length }}
                    {% else %}
                        {{ users|selectattr('is_active')|list|length }}
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Tìm kiếm</label>
            <div class="relative">
                <input type="text" id="searchInput" placeholder="Tìm theo tên, email..." 
                       class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
            </div>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Vai trò</label>
            <select id="roleFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                <option value="">Tất cả</option>
                <option value="admin">Quản trị viên</option>
                <option value="manager">Quản sinh</option>
                <option value="teacher">Giáo viên</option>
            </select>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Trạng thái</label>
            <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                <option value="">Tất cả</option>
                <option value="active">Hoạt động</option>
                <option value="inactive">Không hoạt động</option>
            </select>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Sắp xếp</label>
            <select id="sortBy" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                <option value="name">Tên</option>
                <option value="email">Email</option>
                <option value="role">Vai trò</option>
                <option value="created">Ngày tạo</option>
            </select>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Danh sách người dùng</h3>
    </div>
    
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Người dùng
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Vai trò
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Liên hệ
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Trạng thái
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ngày tạo
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Thao tác
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200" id="usersTableBody">
                {% set user_list = users.items if users.items else users %}
                {% for user in user_list %}
                <tr class="hover:bg-gray-50 transition-colors duration-200 user-row" 
                    data-name="{{ user.full_name.lower() }}" 
                    data-email="{{ user.email.lower() }}"
                    data-role="{{ user.role }}"
                    data-status="{{ 'active' if user.is_active else 'inactive' }}"
                    data-created="{{ user.created_at.strftime('%Y-%m-%d') if user.created_at else '' }}">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <div class="h-10 w-10 rounded-full 
                                    {% if user.role == 'admin' %}bg-red-100
                                    {% elif user.role == 'manager' %}bg-blue-100
                                    {% elif user.role == 'teacher' %}bg-green-100
                                    {% else %}bg-gray-100{% endif %} 
                                    flex items-center justify-center">
                                    <i class="fas 
                                        {% if user.role == 'admin' %}fa-crown text-red-600
                                        {% elif user.role == 'manager' %}fa-user-tie text-blue-600
                                        {% elif user.role == 'teacher' %}fa-chalkboard-teacher text-green-600
                                        {% else %}fa-user text-gray-600{% endif %}"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">{{ user.full_name }}</div>
                                <div class="text-sm text-gray-500">{{ user.username }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {% if user.role == 'admin' %}bg-red-100 text-red-800
                            {% elif user.role == 'manager' %}bg-blue-100 text-blue-800
                            {% elif user.role == 'teacher' %}bg-green-100 text-green-800
                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                            {% if user.role == 'admin' %}
                                <i class="fas fa-crown mr-1"></i> Quản trị viên
                            {% elif user.role == 'manager' %}
                                <i class="fas fa-user-tie mr-1"></i> Quản sinh
                            {% elif user.role == 'teacher' %}
                                <i class="fas fa-chalkboard-teacher mr-1"></i> Giáo viên
                            {% else %}
                                <i class="fas fa-user mr-1"></i> {{ user.role|title }}
                            {% endif %}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ user.email }}</div>
                        <div class="text-sm text-gray-500">{{ user.phone or 'Chưa cập nhật' }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if user.is_active %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check-circle mr-1"></i>
                            Hoạt động
                        </span>
                        {% else %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <i class="fas fa-times-circle mr-1"></i>
                            Không hoạt động
                        </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ user.created_at.strftime('%d/%m/%Y') if user.created_at else 'Không có thông tin' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex items-center justify-end space-x-2">
                            <button onclick="viewUserDetails({{ user.id }})" 
                                    class="text-blue-600 hover:text-blue-900 p-2 rounded-lg hover:bg-blue-50 transition-colors duration-200" 
                                    title="Xem chi tiết">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button onclick="editUser({{ user.id }})" 
                                    class="text-orange-600 hover:text-orange-900 p-2 rounded-lg hover:bg-orange-50 transition-colors duration-200" 
                                    title="Chỉnh sửa">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="toggleUserStatus({{ user.id }}, {{ user.is_active|lower }})" 
                                    class="text-{% if user.is_active %}yellow{% else %}green{% endif %}-600 hover:text-{% if user.is_active %}yellow{% else %}green{% endif %}-900 p-2 rounded-lg hover:bg-{% if user.is_active %}yellow{% else %}green{% endif %}-50 transition-colors duration-200" 
                                    title="{% if user.is_active %}Vô hiệu hóa{% else %}Kích hoạt{% endif %}">
                                <i class="fas fa-{% if user.is_active %}pause{% else %}play{% endif %}"></i>
                            </button>
                            {% if user.id != current_user.id %}
                            <button onclick="deleteUser({{ user.id }}, '{{ user.full_name }}')" 
                                    class="text-red-600 hover:text-red-900 p-2 rounded-lg hover:bg-red-50 transition-colors duration-200" 
                                    title="Xóa">
                                <i class="fas fa-trash"></i>
                            </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        {% if not user_list %}
        <div class="text-center py-12">
            <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Chưa có người dùng nào</h3>
            <p class="text-gray-500 mb-4">Bắt đầu bằng cách tạo người dùng đầu tiên</p>
            <a href="{{ url_for('admin.create_user') }}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700">
                <i class="fas fa-user-plus mr-2"></i>
                Tạo người dùng
            </a>
        </div>
        {% endif %}
    </div>
    
    <!-- Pagination -->
    {% if users.pages and users.pages > 1 %}
    <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div class="flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if users.has_prev %}
                <a href="{{ url_for('admin.users', page=users.prev_num) }}" 
                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Trước
                </a>
                {% endif %}
                {% if users.has_next %}
                <a href="{{ url_for('admin.users', page=users.next_num) }}" 
                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Sau
                </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Hiển thị <span class="font-medium">{{ users.per_page * (users.page - 1) + 1 }}</span>
                        đến <span class="font-medium">{{ users.per_page * users.page if users.page < users.pages else users.total }}</span>
                        trong tổng số <span class="font-medium">{{ users.total }}</span> người dùng
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        {% for page_num in users.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != users.page %}
                                <a href="{{ url_for('admin.users', page=page_num) }}" 
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    {{ page_num }}
                                </a>
                                {% else %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-orange-50 text-sm font-medium text-orange-600">
                                    {{ page_num }}
                                </span>
                                {% endif %}
                            {% else %}
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                ...
                            </span>
                            {% endif %}
                        {% endfor %}
                    </nav>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- User Details Modal -->
<div id="userDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-96 overflow-y-auto">
            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Chi tiết người dùng</h3>
                <button onclick="closeUserDetailsModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="userDetailsContent" class="p-6">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Search and filter functionality
document.getElementById('searchInput').addEventListener('input', filterUsers);
document.getElementById('roleFilter').addEventListener('change', filterUsers);
document.getElementById('statusFilter').addEventListener('change', filterUsers);
document.getElementById('sortBy').addEventListener('change', sortUsers);

function filterUsers() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const roleFilter = document.getElementById('roleFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const rows = document.querySelectorAll('.user-row');
    
    rows.forEach(row => {
        const name = row.dataset.name;
        const email = row.dataset.email;
        const role = row.dataset.role;
        const status = row.dataset.status;
        
        const matchesSearch = name.includes(searchTerm) || email.includes(searchTerm);
        const matchesRole = !roleFilter || role === roleFilter;
        const matchesStatus = !statusFilter || status === statusFilter;
        
        if (matchesSearch && matchesRole && matchesStatus) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function sortUsers() {
    const sortBy = document.getElementById('sortBy').value;
    const tbody = document.getElementById('usersTableBody');
    const rows = Array.from(tbody.querySelectorAll('.user-row'));
    
    rows.sort((a, b) => {
        let aVal, bVal;
        
        switch(sortBy) {
            case 'name':
                aVal = a.dataset.name;
                bVal = b.dataset.name;
                return aVal.localeCompare(bVal);
            case 'email':
                aVal = a.dataset.email;
                bVal = b.dataset.email;
                return aVal.localeCompare(bVal);
            case 'role':
                aVal = a.dataset.role;
                bVal = b.dataset.role;
                return aVal.localeCompare(bVal);
            case 'created':
                aVal = new Date(a.dataset.created);
                bVal = new Date(b.dataset.created);
                return bVal - aVal; // Descending
        }
    });
    
    rows.forEach(row => tbody.appendChild(row));
}

function viewUserDetails(userId) {
    document.getElementById('userDetailsModal').classList.remove('hidden');
    document.getElementById('userDetailsContent').innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin text-orange-500"></i> Đang tải...</div>';
    
    // Load user details via AJAX
    fetch(`/admin/user/${userId}/details`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('userDetailsContent').innerHTML = `
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Họ tên:</label>
                            <p class="text-sm text-gray-900">${data.full_name}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Tên đăng nhập:</label>
                            <p class="text-sm text-gray-900">${data.username}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Email:</label>
                            <p class="text-sm text-gray-900">${data.email}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Vai trò:</label>
                            <p class="text-sm text-gray-900">${data.role_display}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Điện thoại:</label>
                            <p class="text-sm text-gray-900">${data.phone || 'Chưa cập nhật'}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Trạng thái:</label>
                            <p class="text-sm text-gray-900">${data.is_active ? 'Hoạt động' : 'Không hoạt động'}</p>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Địa chỉ:</label>
                        <p class="text-sm text-gray-900">${data.address || 'Chưa cập nhật'}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Ngày tạo:</label>
                        <p class="text-sm text-gray-900">${data.created_at}</p>
                    </div>
                </div>
            `;
        })
        .catch(error => {
            document.getElementById('userDetailsContent').innerHTML = '<div class="text-center py-4 text-red-500"><i class="fas fa-exclamation-triangle"></i> Lỗi khi tải thông tin</div>';
        });
}

function closeUserDetailsModal() {
    document.getElementById('userDetailsModal').classList.add('hidden');
}

function editUser(userId) {
    window.location.href = `/admin/user/${userId}/edit`;
}

function toggleUserStatus(userId, isActive) {
    const action = isActive ? 'vô hiệu hóa' : 'kích hoạt';
    
    if (confirm(`Bạn có chắc chắn muốn ${action} người dùng này?`)) {
        fetch(`/admin/user/${userId}/toggle`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                notify.success(data.message);
                location.reload();
            } else {
                notify.error(data.message || 'Có lỗi xảy ra');
            }
        })
        .catch(error => {
            notify.error('Có lỗi xảy ra khi cập nhật trạng thái');
        });
    }
}

function deleteUser(userId, userName) {
    if (confirm(`Bạn có chắc chắn muốn xóa người dùng "${userName}"?`)) {
        fetch(`/admin/user/${userId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                notify.success(data.message);
                location.reload();
            } else {
                notify.error(data.message || 'Có lỗi xảy ra');
            }
        })
        .catch(error => {
            notify.error('Có lỗi xảy ra khi xóa người dùng');
        });
    }
}

function exportUsers() {
    window.location.href = '{{ url_for("admin.export_users") }}';
}

// Close modal when clicking outside
document.getElementById('userDetailsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeUserDetailsModal();
    }
});

// Animation on load
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('.user-row');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease-out';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 50);
    });
});
</script>
{% endblock %}
